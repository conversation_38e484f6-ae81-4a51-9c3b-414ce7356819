package omni.audit.network.segment.handle.processors;

import com.quanzhi.awdb_core.IpCity;
import com.quanzhi.awdb_core.PositionDto;
import omni.audit.network.segment.handle.entity.IpPositionInfo;
import omni.audit.network.segment.handle.entity.Position;
import omni.audit.network.segment.handle.utils.IpRegionalUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Properties;

public class IpCityResolve {

    protected static List<IpPositionInfo> ipPositionConfigs;
    private static final String enableIpRegional_key = "ip.regional.enable";
    public static Properties handlerProperties = new Properties();
    protected static IpCity ipCity = IpCity.getInstance();
    public static Long lastTime = null;

    static {
        initializeParameter(); // 调用初始化方法
    }

    private static void initializeParameter() {
        new Thread(() -> {
            String awdbPath = "/home/<USER>";
            String wryPath = "/home/<USER>";
            ipCity.init(awdbPath, wryPath);
        }).start();
    }

    public static Position resolveIpCity(String ip, List<IpPositionInfo> ipPositionInfos) {
        ipPositionConfigs = ipPositionInfos;
        return getIpCity(ip, true);
    }
    protected static Position getIpCity(String ip, boolean isMatchConfigIpCity) {
        // 测试
//        ip = "***************";
////        ip = "**************"; // 有问题
        Position position = null;
//        if (isMatchConfigIpCity) {
//            // 获取配置的ip地域信息
//            position = configIpCity(ip);
//        }
        // 离线库解析ip地域信息
        if (position == null) {
            position = offlineIpCity(ip);
        }
        // 解析不了的，用自定义方法再获取一次
        if (handlerProperties.getProperty(enableIpRegional_key) != null
                && Boolean.valueOf(handlerProperties.getProperty(enableIpRegional_key))) {
            if (position == null && !ip.contains(":")) {
                position = IpRegionalUtils.find(ip);
            }
        }
        if (position == null){
            position = IpRegionalUtils.UNKNOWN_POSITION;
        }
        if (position != null) {
            position.setCountry((StringUtils.isEmpty(position.getCountry()) || "局域网".equals(position.getCountry())) ? IpRegionalUtils.UNKNOWN_POSITION_REPLACE : position.getCountry());
            position.setProvince((StringUtils.isEmpty(position.getProvince()) || "局域网".equals(position.getProvince())) ? IpRegionalUtils.UNKNOWN_POSITION_REPLACE : position.getProvince());
            position.setCity((StringUtils.isEmpty(position.getCity()) || "局域网".equals(position.getCity())) ? IpRegionalUtils.UNKNOWN_POSITION_REPLACE : position.getCity());
            position.setLocation(IpRegionalUtils.getLocation(position));
        }
        return position;
    }

    protected static Position offlineIpCity(String ip) {
        Position position = null;
        //initIpCity();

        PositionDto positionDto = ipCity.resolveIpCity(ip);
        if (positionDto != null) {
            position = new Position(positionDto.getCountry(), positionDto.getProvince(), positionDto.getCity(), "");
        }
        return position;
    }

//    public static void initIpCity(){
//        long now = System.currentTimeMillis();
//        // 首次，或读取配置超过30分钟，重新读取配。避免每次都读取，影响效率
//        if (lastTime == null || (now - lastTime > 30 * 60 * 1000)){
//            lastTime = now;
//            new Thread(() -> {
//                //TODO 初始化ip库
//                getHandlerProperties();
//                if (DataUtil.isNotEmpty(handlerProperties.getProperty(ipdbPath_key))) {
//                    ipdbPath = handlerProperties.getProperty(ipdbPath_key);
//                }
//                if (DataUtil.isNotEmpty(handlerProperties.getProperty(ipdbFile_key))) {
//                    awdbFile = handlerProperties.getProperty(ipdbFile_key);
//                }
//                if (DataUtil.isNotEmpty(handlerProperties.getProperty(ipv6File_key))) {
//                    ipv6File = handlerProperties.getProperty(ipv6File_key);
//                }
//                String awdbPath = ipdbPath + awdbFile;
//                String wryPath = ipdbPath + ipv6File;
//                String ipUpdateTime = null;
//                if (DataUtil.isNotEmpty(handlerProperties.getProperty(ipCityUpdateTimeKey))) {
//                    ipUpdateTime = handlerProperties.getProperty(ipCityUpdateTimeKey);
//                }
////                log.info("lastTime:{},ipCityUpdateTime:{},nacos ipUpdateTime:{}",lastTime,ipCityUpdateTime,ipUpdateTime);
//                if (ipCityUpdateTime == null) {
//                    ipCityUpdateTime = ipUpdateTime;
////                    log.info("awreader:{}",IpCity.getIpv4Sevice().getAwReader());
//                    if (IpCity.getIpv4Sevice().getAwReader() == null) {
//                        log.info("init ip-offline-database");
//                        ipCity.init(awdbPath, wryPath);
//                    }
//                } else {
//                    if (ipUpdateTime != null && !ipUpdateTime.equals(ipCityUpdateTime)){
//                        log.info("reload ip-offline-database");
//                        ipCityUpdateTime = ipUpdateTime;
//                        ipCity.reload(awdbPath, wryPath);
//                    }
//                }
//            }).start();
//        }
//    }
}
