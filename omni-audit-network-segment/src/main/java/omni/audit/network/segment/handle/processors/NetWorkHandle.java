package omni.audit.network.segment.handle.processors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import omni.audit.network.segment.handle.entity.*;
import omni.audit.network.segment.handle.enums.RegionEnum;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutionException;

import static java.util.Collections.EMPTY_MAP;

@Slf4j
public class NetWorkHandle {
    //TODO 查询数据库填充
    public List<NetworkSegmentIpInfo> networkSegments = new ArrayList<>();
    private List<IpPositionInfo> ipPositionInfos;
    ObjectMapper objectMapper = new ObjectMapper();

    private static Cache<String, Optional<Map<String, Object>>> ipCache;

    private final static Long ipCacheExpireMs = 120L;

    private final static Long ipCacheMaximumSize = 500000L;

    public NetWorkHandle(){
        ipCache = CacheBuilder.newBuilder().maximumSize(ipCacheMaximumSize).expireAfterWrite(Duration.ofMinutes(ipCacheExpireMs)).recordStats().build();

    }

    public Map<String,Object> handle(String ip) {

        return getNetworkDomainsAndArea(ip);
    }

    protected Map<String,Object> getNetworkDomainsAndArea(String ip) {
        return NetworkDomainResolve.getNetworkDomainsAndArea(ip, networkSegments);
    }

    private Map<String, Object> loadIpInfo(String ip) {
        IpInfo ipInfo = new IpInfo();
        //1、获取国家、省份、城市
        Position position = IpCityResolve.resolveIpCity(ip, ipPositionInfos);
        ipInfo.setCountry(position.getCountry());
        ipInfo.setProvince(position.getProvince());
        ipInfo.setCity(position.getCity());
        //2、获取地域
        RegionEnum regionEnum = resolveRegion(position);
        ipInfo.setRegional(regionEnum.name());
        //3、获取访问域
        List<NetworkDomain> networkDomains = NetworkDomainResolve.getNetworkDomains(ip, networkSegments);
        List<String> networkDomainIds = new ArrayList<>();
        for (NetworkDomain networkDomain : networkDomains) {
            networkDomainIds.add(networkDomain.getId());
        }
        ipInfo.setVisitDomains(networkDomainIds);
        ipInfo.setNetworkDomains(networkDomainIds);

        Map<String, Object> ipInfoMap = (Map<String, Object>) objectMapper.convertValue(ipInfo,Map.class);
        return ipInfoMap;
    }

    public RegionEnum resolveRegion(Position position) {
        if (position.getCountry().equals("局域网")
                || position.getCountry().equals("其他")
                || (position.getCountry().equals("中国") && (!position.getProvince().equals("香港") || !position.getProvince().equals("澳门") || !position.getProvince().equals("台湾")))) {
            return RegionEnum.IN_COUNTRY;
        } else {
            return RegionEnum.OVERSEAS;
        }
    }

}
