package omni.audit.network.segment.handle.processors;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import omni.audit.network.segment.handle.entity.NetworkDomain;
import omni.audit.network.segment.handle.entity.NetworkSegment;
import omni.audit.network.segment.handle.entity.NetworkSegmentIpInfo;
import omni.audit.network.segment.handle.entity.Position;
import omni.audit.network.segment.handle.utils.IpRegionalUtils;
import omni.audit.network.segment.handle.utils.IpUtils;
import omni.audit.network.segment.handle.utils.IpV6Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.*;

import static omni.audit.network.segment.handle.processors.IpCityResolve.getIpCity;

@Slf4j
public class NetworkDomainResolve {
    protected static List<NetworkSegmentIpInfo> networkSegments;

    public static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 默认缺省的局域网-其他网段，since 3.2.0 产品新增了**********-***************和***********-***************两个段
     */
    private static List<String> defaultLocalNetworkIps = Arrays.asList("***********-***************","**********-**************","10.0.0.0-**************","**********-***************","***********-***************");

    /**
     * 默认的局域网-其他 的网段信息
     */
    private static List<NetworkSegmentIpInfo> defaultLocalNetworkSegments = new ArrayList<>();

    static {
        initDefaultLocalNetworkSegments();
    }

    private static void initDefaultLocalNetworkSegments() {
        NetworkSegment networkSegment = new NetworkSegment();
        networkSegment.setId("局域网-其他");
        networkSegment.setDomain(new NetworkSegment.Domain("局域网", "其他", null));
        List<NetworkSegment.NetworkSegmentIp> list = new ArrayList<>();
        for (String ipSeg : defaultLocalNetworkIps) {
            NetworkSegment.NetworkSegmentIp ip = new NetworkSegment.NetworkSegmentIp();
            String[] ipArr = ipSeg.split("-");
            ip.setStartIp(ipArr[0]);
            ip.setEndIp(ipArr[1]);
            ip.setIpType(2);
            list.add(ip);
        }
        networkSegment.setNetworkSegmentList(list);
        defaultLocalNetworkSegments = convert2(Arrays.asList(networkSegment));
    }


    public static List<NetworkDomain> getNetworkDomains(String ip, List<NetworkSegmentIpInfo> networkSegmentConfigInfos) {
        networkSegments = networkSegmentConfigInfos;
        return getNetworkDomains(ip);
    }

    public static Map<String,Object> getNetworkDomainsAndArea(String ip, List<NetworkSegmentIpInfo> networkSegmentConfigInfos) {
        networkSegments = networkSegmentConfigInfos;
        return getNetworkDomainsAndArea(ip);
    }

    protected static Map<String,Object> getNetworkDomainsAndArea(String ip) {
        return matchNetworkDomainsAndArea(ip);
    }

    protected static Map<String,Object> matchNetworkDomainsAndArea(String ip) {
        Map<String,Object> result = new HashMap<>();
        BigInteger ipNum = IpUtils.ip2Int(ip);
        // 1.首先匹配客户已经配置的网段
        List<NetworkDomain> networkDomains = matchIpByComputer(ipNum, ip, networkSegments);
        if(CollectionUtil.isNotEmpty(networkDomains)){
            result.put("network",networkDomains);
            Position configPosition = getPosition(networkDomains);
            result.put("area",configPosition);
        }else{
            // 2.匹配不到再匹配默认缺省的局域网-其他网段
            networkDomains = matchIpByComputer(ipNum, ip, defaultLocalNetworkSegments);
            if(CollectionUtil.isNotEmpty(networkDomains)){
                result.put("network",networkDomains);
                result.put("area", IpRegionalUtils.UNKNOWN_POSITION);
            }else{
                // 3.还是匹配不到再根据地域识别打互联网-境内或互联网-境外网段
                Position position = getIpCity(ip, false);
                networkDomains = getNetworkDomainsByCountry(position);
                result.put("network",networkDomains);
                result.put("area",position);
            }
        }
        return result;
    }

    public static Position getPosition(List<NetworkDomain> networkDomains){
        if(networkDomains.isEmpty()){
            return IpRegionalUtils.UNKNOWN_POSITION;
        }
        /**
         * 已经识别到的网段配了地域就更新，这个流程在ipCityResolve后面，会根据配置覆盖离线库识别到的地域
         * 考虑4种情况：
         * 1. 识别到自己配置的网段有地域，那就一定要打上一个
         * 2. 识别到自己配置的网段都没有地域，那最后就是其他
         * 3. 局域网-其他网段地域应该是其他
         * 4. 互联网-境内或者互联网-境外地域就是前面离线库识别到的
         */
        for (NetworkDomain accessDomain : networkDomains) {
            // 注意：这里不能用accessDomain.getPosition().getCountry() != null 来判断，因为配置里面空的都是一个空字符串，不是null，写错会导致后面真正配置了地域的，没有打上，提前退出，最后变成了其他
            if (accessDomain.getPosition() != null && StringUtils.isNotEmpty(accessDomain.getPosition().getCountry())
                    && StringUtils.isNotEmpty(accessDomain.getPosition().getProvince()) && StringUtils.isNotEmpty(accessDomain.getPosition().getCity())) {
                return accessDomain.getPosition();
            }
        }
        // 这是第一优先级（就是自己配置的网段）如果都没有配置地域，那就是其他，这个也跟原来的逻辑一样的
        return IpRegionalUtils.UNKNOWN_POSITION;
    }


    protected static List<NetworkDomain> getNetworkDomains(String ip) {
        return matchNetworkDomains(ip);
    }

    protected static List<NetworkDomain> matchNetworkDomains(String ip) {
        BigInteger ipNum = IpUtils.ip2Int(ip);
        // 1.首先匹配客户已经配置的网段
        List<NetworkDomain> networkDomains = matchIpByComputer(ipNum, ip, networkSegments);
        // 2.匹配不到再匹配默认缺省的局域网-其他网段
        if (networkDomains.isEmpty()) {
            networkDomains = matchIpByComputer(ipNum, ip, defaultLocalNetworkSegments);
        }
        // 3.还是匹配不到再根据地域识别打互联网-境内或互联网-境外网段
        if (networkDomains.isEmpty()) {
            networkDomains = getOtherDefaultNetworkDomains(ip);
        }
        return networkDomains;
    }


    private static List<NetworkDomain> matchIpByComputer(BigInteger ipNum, String ip, List<NetworkSegmentIpInfo> networkSegments) {
        List<NetworkDomain> networkDomains = new ArrayList<>();
        if (ipNum == null) {
            return networkDomains;
        }
        try {
            for (NetworkSegmentIpInfo segmentIpInfo : networkSegments) {
                if (segmentIpInfo == null) {
                    continue;
                }
                List<NetworkSegmentIpInfo.IpInfo> ipInfos = segmentIpInfo.getSegmentIpInfos();
                if (ipInfos == null) {
                    continue;
                }
                for (NetworkSegmentIpInfo.IpInfo ipInfo : ipInfos) {
                    // IPv6不能打上IPv4的网段
                    if(ip.contains(":") && ipInfo.getIpv4Flag()){
                        continue;
                    }
                    if(!ip.contains(":") && !ipInfo.getIpv4Flag()){
                        continue;
                    }
                    if (ipInfo != null && ipInfo.getIps() != null && ipInfo.getIpe() != null) {
                        if (ipNum.compareTo(ipInfo.getIps()) >= 0 && ipNum.compareTo(ipInfo.getIpe()) <= 0) {
                            networkDomains.add(getNetworkDomain(segmentIpInfo));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("ip:{} matchIpByComputer error:", ip,e);
        }
        return networkDomains;
    }

    private static NetworkDomain getNetworkDomain(NetworkSegment networkSegment) {
        NetworkDomain networkDomain = new NetworkDomain();
        networkDomain.setId(networkSegment.getId());
        networkDomain.setFirstLevel(networkSegment.getDomain().getFirstLevel());
        networkDomain.setPosition(networkSegment.getPosition());
        networkDomain.setRegion(networkSegment.getRegion());
        return networkDomain;
    }

    private static List<NetworkDomain> getOtherDefaultNetworkDomains(String ip) {
        Position position = getIpCity(ip, false);
        List<NetworkDomain> networkDomains = getNetworkDomainsByCountry(position);
        return networkDomains;
    }

    private static List<NetworkDomain> getNetworkDomainsByCountry(Position position) {
        if (position == null) {
            return Collections.emptyList();
        }
        List<NetworkDomain> networkDomains = new ArrayList<>();
        NetworkDomain networkDomain = new NetworkDomain();
        String country = position.getCountry();
        String province = position.getProvince();
        if (StringUtils.isNotEmpty(country)) {
            if ("中国".equals(country) || "其他".equals(country)) {
                if ("香港".equals(province) || "澳门".equals(province) || "台湾".equals(province)) {
                    networkDomain.setId("互联网-境外");
                    networkDomain.setFirstLevel("互联网");
                    networkDomain.setSecondLevel("境外");
                } else {
                    networkDomain.setId("互联网-境内");
                    networkDomain.setFirstLevel("互联网");
                    networkDomain.setSecondLevel("境内");
                }
            } else {
                networkDomain.setId("互联网-境外");
                networkDomain.setFirstLevel("互联网");
                networkDomain.setSecondLevel("境外");
            }
        }
        networkDomains.add(networkDomain);
        return networkDomains;
    }
    public static List<NetworkSegmentIpInfo> convert2(List<NetworkSegment> networkSegments) {
        List<NetworkSegmentIpInfo> networkSegmentIpInfos = new ArrayList<>();
        // 先全部转换为字符串类型，人容易看懂的
        for (NetworkSegment networkSegment : networkSegments) {
            List<NetworkSegment.NetworkSegmentIp> networkSegmentList = networkSegment.getNetworkSegmentList();
            List<NetworkSegment.NetworkSegmentIp> newNetworkSegmentList=new ArrayList<>();
            for(NetworkSegment.NetworkSegmentIp networkSegmentIp:networkSegmentList){
                try {
                    format(networkSegmentIp);
                    newNetworkSegmentList.add(networkSegmentIp);
                } catch (Exception e) {
                    log.error("network ip:{} format error:",networkSegmentIp,e);
                }
            }
            // 过滤掉一些转换报错的网段
            networkSegment.setNetworkSegmentList(newNetworkSegmentList);
        }
        // 再转换出数字类型，程序中直接比较的
        for (NetworkSegment networkSegment : networkSegments) {
            NetworkSegmentIpInfo networkSegmentIpInfo = objectMapper.convertValue(networkSegment, NetworkSegmentIpInfo.class);
            List<NetworkSegmentIpInfo.IpInfo> ipInfos = new ArrayList<>(networkSegment.getNetworkSegmentList().size());
            for (NetworkSegment.NetworkSegmentIp networkSegmentIp : networkSegment.getNetworkSegmentList()) {
                NetworkSegmentIpInfo.IpInfo ipInfo = new NetworkSegmentIpInfo.IpInfo();
                fillIpNum(networkSegmentIp, ipInfo);
                ipInfos.add(ipInfo);
            }
            networkSegmentIpInfo.setSegmentIpInfos(ipInfos);
            networkSegmentIpInfos.add(networkSegmentIpInfo);
        }
        // 只有默认局域网的就不管了
        //TODO 通知更新
//        if(networkSegments != null && networkSegments.size() >= 3){
//            try {
//                SpringEventPublisherUtils.publishEvent(new NetworkUpdateEvent(networkSegmentIpInfos,null,System.currentTimeMillis()));
//            }catch (Exception e){
//                log.error("network modify publisher error:",e);
//            }
//        }
        return networkSegmentIpInfos;
    }

    private static void format(NetworkSegment.NetworkSegmentIp networkSegmentIp) {
        Integer ipType = networkSegmentIp.getIpType();
        String originStartIp = networkSegmentIp.getStartIp().trim();
        String startIp = null;
        String endIp = null;
        if (NetworkSegment.IpTypeEnum.IP.value().equals(ipType)) {
            if (IpV6Util.isIpv6(originStartIp)) {
                startIp = IpV6Util.ipV6UnifiedFormat(originStartIp);
                endIp = startIp;
            } else {
                // 包含通配符的IPv4地址要转换成一个地址段
                if(originStartIp.contains("*")){
                    startIp = originStartIp.replaceAll("\\*", "0");
                    endIp = originStartIp.replaceAll("\\*", "255");
                }else{
                    startIp = originStartIp;
                    endIp = startIp;
                }
            }
        } else if (NetworkSegment.IpTypeEnum.IP_SEG.value().equals(ipType)) {
            String originEndIp = networkSegmentIp.getEndIp().trim();
            if (IpV6Util.isIpv6(originStartIp)) {
                startIp = IpV6Util.ipV6UnifiedFormat(originStartIp);
                endIp = IpV6Util.ipV6UnifiedFormat(originEndIp);
            } else {
                startIp = originStartIp;
                endIp = originEndIp;
            }
        } else if (NetworkSegment.IpTypeEnum.IP_MASK.value().equals(ipType)) {
            List<String> minMaxIps = IpUtils.ipMask2ipSeg(originStartIp);
            startIp = minMaxIps.get(0).trim();
            endIp = minMaxIps.get(1).trim();
        }
        networkSegmentIp.setStartIp(startIp);
        networkSegmentIp.setEndIp(endIp);
    }

    public static void fillIpNum(NetworkSegment.NetworkSegmentIp networkSegmentIp, NetworkSegmentIpInfo.IpInfo ipInfo) {
        // TODO:测试一下这些地址转化：0.0.0.0 :: *************** FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF:FFFF
        String startIp = networkSegmentIp.getStartIp();
        String endIp = networkSegmentIp.getEndIp();
        BigInteger ips = null;
        BigInteger ipe = null;
        if (IpV6Util.isIpv6(startIp)) {
            ips = IpV6Util.ipv6toInt(startIp);
            ipe = IpV6Util.ipv6toInt(endIp);
            ipInfo.setIpv4Flag(false);
        } else {
            ips = ipv4ToInt(startIp);
            ipe = ipv4ToInt(endIp);
            ipInfo.setIpv4Flag(true);
        }
        ipInfo.setStartIp(startIp);
        ipInfo.setEndIp(endIp);
        ipInfo.setIpType(networkSegmentIp.getIpType());
        ipInfo.setIps(ips);
        ipInfo.setIpe(ipe);
    }

    private static BigInteger ipv4ToInt(String ipv4) {
        long result = 0;
        String[] split = ipv4.split("\\.");
        if (split.length != 4) {
            return null;
        }
        for (int i = 0; i < split.length; i++) {
            int s = Integer.valueOf(split[i].split("\\\\")[0]);
            if (s > 255) {
                return null;
            }
            result = (result << 8) | s;
        }

        return BigInteger.valueOf(result);
    }
}
