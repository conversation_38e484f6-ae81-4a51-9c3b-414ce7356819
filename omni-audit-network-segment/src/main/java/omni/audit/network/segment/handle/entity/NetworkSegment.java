package omni.audit.network.segment.handle.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class NetworkSegment implements Serializable {

    private String id;

    private List<NetworkSegmentIp> networkSegmentList = new ArrayList<NetworkSegmentIp>();

    /**
     * 网络域
     * 最多只支持3级配置
     */
    private Domain domain;

    /**
     * 地域
     */
    @Deprecated
    private String region;

    /**
     * 地理位置
     */
    private Position position;

    /**
     * @see TypeEnum
     */
    private Integer type;

    public static enum TypeEnum {

        /**
         * 系统内置
         */
        SYSTEM(0),

        /**
         * 自定义
         */
        CUSTOM(1);

        private int val;

        TypeEnum(int val) {
            this.val = val;
        }

        public Integer val() {
            return this.val;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Domain implements Serializable {

        private String firstLevel;

        private String secondLevel;

        private String thirdLevel;
    }

    @Data
    public static class NetworkSegmentIp implements Serializable {

        private String startIp;

        private String endIp;

        private Integer ipType;

    }

    /**
     * 支持3种ip配置方式
     * 单IP 1
     * IP范围 2
     * 子网掩码 3
     */

    public static enum IpTypeEnum {

        IP(1),

        IP_SEG(2),

        IP_MASK(3);

        private Integer val;

        IpTypeEnum(int val) {
            this.val = val;
        }

        public Integer value() {
            return this.val;
        }

        public static final IpTypeEnum valOf(int val){
            for (IpTypeEnum ipTypeEnum : values()){
                if (ipTypeEnum.val == val){
                    return ipTypeEnum;
                }
            }
            return null;
        }
    }
}
