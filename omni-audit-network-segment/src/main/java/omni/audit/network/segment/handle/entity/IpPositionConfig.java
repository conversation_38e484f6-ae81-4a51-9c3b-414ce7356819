package omni.audit.network.segment.handle.entity;

import lombok.Data;

import java.util.List;

@Data
public class IpPositionConfig {

    private String id;

    /**
     * IP列表
     */
    private List<String> ips;

    /**
     * IP段
     */
    private List<String> ipSeg;

    private String country;

    private String province;

    private String city;

    private MatchTypeEnum matchType;

    /**
     * 接口配置状态枚举类
     */
    public enum MatchTypeEnum {

        /**
         * 多个IP
         */
        IPS,

        /**
         * IP段
         */
        IP_SEG,

    }
}
