package omni.audit.network.segment.handle.utils;

import com.quanzhi.awdb_core.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * create at 2022/4/19 11:11 上午
 * @description:
 **/
@Slf4j
public class IpV6Util {
    private static final int IPV6Length = 8; // IPV6地址的分段
    private static final int IPV4Length = 4; // IPV6地址分段
    private static final int IPV4ParmLength = 2; // 一个IPV4分段占的长度
    private static final int IPV6ParmLength = 4; // 一个IPV6分段占的长
    //IPV6校验正则
    private static Pattern patIpV6;
    private static Pattern patIpV4;
    private static final char[] map = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    static {
        patIpV4 = Pattern.compile("^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])\\.){3}(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5])$");
        patIpV6 = Pattern.compile("^((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))(%.+)?$");
    }

    /**
     * <AUTHOR>
     * @date 2022/4/21 2:05 PM
     * @Description 判断是否ipv6
     * @param ip
     * @return boolean
     * @Since
     */
    public static boolean isIpv6(String ip){
        return IpUtil.isIPV6(ip);
    }

    /**
     * 数字转IPV6地址
     *
     * @param big IPV6地址的数字
     * @return 返回值
     */
    public String int2ipv6(BigInteger big) {
        String str = "";
        BigInteger ff = BigInteger.valueOf(0xffff);
        for (int i = 0; i < 8; i++) {
            str = big.and(ff).toString(16) + ":" + str;
            big = big.shiftRight(16);
        }
        str = str.substring(0, str.length() - 1);
        String ipV6 = str.replaceFirst("(^|:)(0+(:|$)){2,8}", "::");
        return ipV6UnifiedFormat(ipV6);
    }

    /**
     * 十六进制串转化为IP地址
     *
     * @param key 十六进制串
     * @return 返回值
     */
    private static String splitKey(String key) {
        String IPV6Address = "";
        String IPAddress = "";
        String strKey = "";
        // 将ip按：分隔
        while (!"".equals(key)) {
            strKey = key.substring(0, 4);
            key = key.substring(4);
            if ("".equals(IPV6Address)) {
                IPV6Address = strKey;
            } else {
                IPV6Address += ":" + strKey;
            }
        }
        IPAddress = IPV6Address;
        return IPAddress;
    }

    /**
     * ipV6地址转数字
     *
     * @param ipv6 ipv6地址
     * @return 返回值
     */
    public static BigInteger ipv6toInt(String ipv6) {
        try {
            int compressIndex = ipv6.indexOf("::");
            if (compressIndex != -1) {
                String part1s = ipv6.substring(0, compressIndex);
                String part2s = ipv6.substring(compressIndex + 1);
                BigInteger part1 = ipv6toInt(part1s);
                BigInteger part2 = ipv6toInt(part2s);
                int part1hasDot = 0;
                char ch[] = part1s.toCharArray();
                for (char c : ch) {
                    if (c == ':') {
                        part1hasDot++;
                    }
                }
                return part1.shiftLeft(16 * (7 - part1hasDot)).add(part2);
            }
            String[] str = ipv6.split(":");
            BigInteger big = BigInteger.ZERO;
            for (int i = 0; i < str.length; i++) {
                if (str[i].isEmpty()) {
                    str[i] = "0";
                }
                big = big.add(BigInteger.valueOf(Long.valueOf(str[i], 16))
                        .shiftLeft(16 * (str.length - i - 1)));
            }
            return big;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将所有的ip地址转换为统一的IPV6表示方法
     *
     * @param ipV6 ipv6地址
     */
    public static String ipV6UnifiedFormat(String ipV6) {
        String key = buildKey(ipV6);
        return splitKey(key);
    }

    /**
     * IPV6地址的单个校验
     *
     * @param ipV6 ipV6地址
     * @return 返回值
     */
    public static boolean singleIpV6Check(String ipV6) {
        return patIpV6.matcher(ipV6).matches();
    }

    /**
     * IPV4地址的单个校验
     *
     * @param ipV4 ipV4地址
     * @return 返回值
     */
    public static boolean singleIpV4Check(String ipV4) {
        return patIpV4.matcher(ipV4).matches();
    }

    /**
     * IPV6、IPV4转化为十六进制串
     *
     * @param ipAddress IP地址
     * @return 返回值
     */
    private static String buildKey(String ipAddress) {
        String Key = "";
        // ipv4标识 。判断是否是ipv4地址
        int dotFlag = ipAddress.indexOf(".");
        // ipv6标识 。判断是否是ipv6地址
        int colonFlag = ipAddress.indexOf(":");
        // ipv6标识 。判断是否是简写的ipv6地址
        int dColonFlag = ipAddress.indexOf("::");
        // 将v6或v4的分隔符用&代替
        ipAddress = ipAddress.replace(".", "&");
        ipAddress = ipAddress.replace(":", "&");
        // ipv4 address。将ipv4地址转换成16进制的形式
        if (dotFlag != -1 && colonFlag == -1) {
            String[] arr = ipAddress.split("&", -1);
            // 1、 ipv4转ipv6，前4组数补0或f
            for (int i = 0; i < IPV6Length - IPV4ParmLength; i++) {
                // 根据v4转v6的形式，除第4组数补ffff外，前3组数补0000
                if (i == IPV6Length - IPV4ParmLength - 1) {
                    Key += "ffff";
                } else {
                    Key += "0000";
                }
            }
            // 2、将ipv4地址转成16进制
            for (int j = 0; j < IPV4Length; j++) {
                // 1)将每组ipv4地址转换成16进制
                arr[j] = Integer.toHexString(Integer.parseInt(arr[j]));
                // 2) 位数不足补0，ipv4地址中一组可转换成一个十六进制，两组数即可标识ipv6中的一组，v6中的一组数不足4位补0
                for (int k = 0; k < (IPV4ParmLength - arr[j].length()); k++) {
                    Key += "0";
                }
                Key += arr[j];
            }
        }
        // Mixed address with ipv4 and ipv6。将v4与v6的混合地址转换成16进制的形式
        if (dotFlag != -1 && colonFlag != -1 && dColonFlag == -1) {
            String[] arr = ipAddress.split("&", -1);

            for (int i = 0; i < IPV6Length - IPV4ParmLength; i++) {
                // 将ip地址中每组不足4位的补0
                for (int k = 0; k < (IPV6ParmLength - arr[i].length()); k++) {
                    Key += "0";
                }
                Key += arr[i];
            }

            for (int j = arr.length - IPV4Length; j < arr.length; j++) {
                arr[j] = Integer.toHexString(Integer.parseInt(arr[j]));
                for (int k = 0; k < (IPV4ParmLength - arr[j].length()); k++) {
                    Key += "0";
                }
                Key += arr[j];
            }
        }
        // Mixed address with ipv4 and ipv6,and there are more than one
        // '0'。将v4与v6的混合地址(如::32:dc:**************)转换成16进制的形式
        // address param
        if (dColonFlag != -1 && dotFlag != -1) {
            String[] arr = ipAddress.split("&", -1);
            // 存放16进制的形式
            String[] arrParams = new String[IPV6Length + IPV4ParmLength];
            int indexFlag = 0;
            int pFlag = 0;
            // 1、将简写的ip地址补0
            // 如果ip地址中前面部分采用简写，做如下处理
            if ("".equals(arr[0])) {
                // 1)如果ip地址采用简写形式，不足位置补0，存放到arrParams中
                for (int j = 0; j < (IPV6Length + IPV4ParmLength - (arr.length - 2)); j++) {
                    arrParams[j] = "0000";
                    indexFlag++;
                }
                // 2)将已有值的部分(如32:dc:**************)存放到arrParams中
                for (int i = 2; i < arr.length; i++) {
                    arrParams[indexFlag] = arr[i];
                    indexFlag++;
                }
            } else {
                for (int i = 0; i < arr.length; i++) {
                    if ("".equals(arr[i])) {
                        for (int j = 0; j < (IPV6Length + IPV4ParmLength
                                - arr.length + 1); j++) {
                            arrParams[indexFlag] = "0000";
                            indexFlag++;
                        }
                    } else {
                        arrParams[indexFlag] = arr[i];
                        indexFlag++;
                    }
                }
            }
            // 2、ip(去除ipv4的部分)中采用4位十六进制数表示一组数，将不足4位的十六进制数补0
            for (int i = 0; i < IPV6Length - IPV4ParmLength; i++) {
                // 如果arrParams[i]组数据不足4位，前补0
                for (int k = 0; k < (IPV6ParmLength - arrParams[i].length()); k++) {
                    Key += "0";
                }
                Key += arrParams[i];
                // pFlag用于标识位置，主要用来标识ipv4地址的起始位
                pFlag++;
            }
            // 3、将ipv4地址转成16进制
            for (int j = 0; j < IPV4Length; j++) {
                // 1)将每组ipv4地址转换成16进制
                arrParams[pFlag] = Integer.toHexString(Integer
                        .parseInt(arrParams[pFlag]));
                // 2)位数不足补0，ipv4地址中一组可转换成一个十六进制，两组数即可标识ipv6中的一组，v6中的一组数不足4位补0
                for (int k = 0; k < (IPV4ParmLength - arrParams[pFlag].length()); k++) {
                    Key += "0";
                }
                Key += arrParams[pFlag];
                pFlag++;
            }
        }
        // ipv6 address。将ipv6地址转换成16进制
        if (dColonFlag == -1 && dotFlag == -1 && colonFlag != -1) {
            String[] arrParams = ipAddress.split("&", -1);
            // 将v6地址转成十六进制
            for (int i = 0; i < IPV6Length; i++) {
                // 将ipv6地址中每组不足4位的补0
                for (int k = 0; k < (IPV6ParmLength - arrParams[i].length()); k++) {
                    Key += "0";
                }

                Key += arrParams[i];
            }
        }
        //省略格式IP解析
        if (dColonFlag != -1 && dotFlag == -1) {
            String[] arr = ipAddress.split("&", -1);
            String[] arrNew = null;
            if (arr[arr.length - 1].equals(arr[arr.length - 2]) && "".equals(arr[arr.length - 1])) {
                arrNew = new String[arr.length - 1];
                for (int i = 0; i < arr.length - 1; i++) {
                    arrNew[i] = arr[i];
                }
            } else {
                arrNew = new String[arr.length];
                arrNew = arr;
            }
            String[] arrParams = new String[IPV6Length];
            int indexFlag = 0;
            if ("".equals(arrNew[0])) {
                for (int j = 0; j < (IPV6Length - (arrNew.length - 2)); j++) {
                    arrParams[j] = "0000";
                    indexFlag++;
                }
                for (int i = 2; i < arrNew.length; i++) {
                    arrParams[indexFlag] = arrNew[i];
                    indexFlag++;
                }
            } else {
                for (int i = 0; i < arrNew.length; i++) {
                    if ("".equals(arrNew[i])) {
                        for (int j = 0; j < (IPV6Length - arrNew.length + 1); j++) {
                            arrParams[indexFlag] = "0000";
                            indexFlag++;
                        }
                    } else {
                        arrParams[indexFlag] = arrNew[i];
                        indexFlag++;
                    }
                }
            }
            for (int i = 0; i < IPV6Length; i++) {
                for (int k = 0; k < (IPV6ParmLength - arrParams[i].length()); k++) {
                    Key += "0";
                }
                Key += arrParams[i];
            }
        }
        return Key.toUpperCase();
    }

    /**
     * 把ipv6掩码（相对ipv4说的）
     * 例如：16  转换为 x:x:x:x:x:x:x:x 的形式
     *
     * @param ipv6mask ipv6掩码
     * @return 返回值
     */
    public static String ipV6MaskIntToString(int ipv6mask) {
        int first = 0;
        int second = 0;
        int third = 0;
        int forth = 0;
        int fifth = 0;
        int sixth = 0;
        int seventh = 0;
        int eighth = 0;
        int i = 0;
        int mi = 0;
        if (ipv6mask < 0 || ipv6mask > 128) {
            return "The mask valued " + ipv6mask + " is invalid!";
        }
        if (ipv6mask == 0) {
            return "0:0:0:0:0:0:0:0";
        } else if (ipv6mask >= 1 && ipv6mask <= 16) {
            mi = 15;
            for (i = 0; i < ipv6mask; i++) {
                first += Math.pow(2, mi);
                mi--;
            }
            second = 0;
            third = 0;
            forth = 0;
            fifth = 0;
            sixth = 0;
            seventh = 0;
            eighth = 0;
        } else if (ipv6mask >= 17 && ipv6mask <= 32) {
            first = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 16; i++) {
                second += Math.pow(2, mi);
                mi--;
            }
            third = 0;
            forth = 0;
            fifth = 0;
            sixth = 0;
            seventh = 0;
            eighth = 0;
        } else if (ipv6mask >= 33 && ipv6mask <= 48) {
            first = 65535;
            second = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 32; i++) {
                third += Math.pow(2, mi);
                mi--;
            }
            forth = 0;
            fifth = 0;
            sixth = 0;
            seventh = 0;
            eighth = 0;
        } else if (ipv6mask >= 49 && ipv6mask <= 64) {
            first = 65535;
            second = 65535;
            third = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 48; i++) {
                forth += Math.pow(2, mi);
                mi--;
            }
            fifth = 0;
            sixth = 0;
            seventh = 0;
            eighth = 0;
        } else if (ipv6mask >= 65 && ipv6mask <= 80) {
            first = 65535;
            second = 65535;
            third = 65535;
            forth = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 64; i++) {
                fifth += Math.pow(2, mi);
                mi--;
            }
            sixth = 0;
            seventh = 0;
            eighth = 0;
        } else if (ipv6mask >= 81 && ipv6mask <= 96) {
            first = 65535;
            second = 65535;
            third = 65535;
            forth = 65535;
            fifth = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 80; i++) {
                sixth += Math.pow(2, mi);
                mi--;
            }
            seventh = 0;
            eighth = 0;
        } else if (ipv6mask >= 97 && ipv6mask <= 112) {
            first = 65535;
            second = 65535;
            third = 65535;
            forth = 65535;
            fifth = 65535;
            sixth = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 96; i++) {
                seventh += Math.pow(2, mi);
                mi--;
            }
            eighth = 0;
        } else if (ipv6mask >= 113 && ipv6mask <= 128) {
            first = 65535;
            second = 65535;
            third = 65535;
            forth = 65535;
            fifth = 65535;
            sixth = 65535;
            seventh = 65535;
            mi = 15;
            for (i = 0; i < ipv6mask - 112; i++) {
                eighth += Math.pow(2, mi);
                mi--;
            }
        }
        return "" + toHex(first) + ":" + toHex(second) + ":" + toHex(third) + ":"
                + toHex(forth) + ":" + toHex(fifth) + ":" + toHex(sixth) + ":" + toHex(seventh) + ":" + toHex(eighth) + "";
    }

    /**
     * 十进制转16进制
     *
     * @param num 十进制数
     * @return 返回值
     */
    public static String toHex(int num) {
        if (num == 0) {
            return "0";
        }
        String result = "";
        while (num != 0) {
            int x = num & 0xF;
            result = map[(x)] + result;
            num = (num >>> 4);
        }
        return result;
    }

    /**
     * ipv6与掩码（相对ipv4的掩码）计算网段
     *
     * @return 返回值
     */
    public static String ipV6Start(String ipv6, String mask) {
        ipv6 = ipV6UnifiedFormat(ipv6);
        List<Integer> dest_ip = new ArrayList<Integer>();
        List<String> ip = new ArrayList<String>();
        String[] ip_split;
        String[] mask_split;
        int i = 0;
        ip_split = ipv6.split(":");
        mask = ipV6MaskIntToString(Integer.valueOf(mask));
        mask_split = mask.split(":");
        String a = "";
        String b = "";
        try {
            for (i = 0; i < 8; i++) {
                a = "".equals(ip_split[i].replaceAll("^(0+)", "")) ? "0" : ip_split[i].replaceAll("^(0+)", "").toLowerCase();
                b = "".equals(mask_split[i].replaceAll("^(0+)", "")) ? "0" : mask_split[i].replaceAll("^(0+)", "").toLowerCase();
                dest_ip.add(Integer.valueOf(a, 16) & Integer.valueOf(b, 16));
            }
            for (i = 0; i < 8; i++) {
                if (i == 8) {
                    ip.add(toHex(dest_ip.get(i)));
                }
                ip.add(toHex(dest_ip.get(i)) + ":");
            }
        } catch (NumberFormatException e) {
            return "false";
        }
        return initaddr(ip);
    }

    /**
     * ipv6与掩码（相对ipv4的掩码）计算网段
     *
     * @return 返回值
     */
    public static String ipV6End(String ipv6, String mask) {
        ipv6 = ipV6UnifiedFormat(ipv6);
        List<Integer> dest_ip = new ArrayList<Integer>();
        List<String> ip = new ArrayList<String>();
        String[] ip_split;
        String[] mask_split;
        int i = 0;
        ip_split = ipv6.split(":");
        mask = ipV6MaskIntToString(Integer.valueOf(mask));
        mask_split = mask.split(":");
        String a = "";
        String b = "";
        try {
            for (i = 0; i < 8; i++) {
                a = "".equals(ip_split[i].replaceAll("^(0+)", "")) ? "0" : ip_split[i].replaceAll("^(0+)", "").toLowerCase();
                b = "".equals(mask_split[i].replaceAll("^(0+)", "")) ? "0" : mask_split[i].replaceAll("^(0+)", "").toLowerCase();
                String aString = padLeft(Integer.toBinaryString(Integer.valueOf(a, 16)), 16, '0');
                String bString = padLeft(Integer.toBinaryString(Integer.valueOf(b, 16)), 16, '0');
                dest_ip.add(getdealStr(aString, bString));
            }
            for (i = 0; i < 8; i++) {
                if (i == 8) {
                    ip.add(toHex(dest_ip.get(i)));
                }
                ip.add(toHex(dest_ip.get(i)) + ":");
            }
        } catch (NumberFormatException e) {
            return "false";
        }
        return initaddr(ip);
    }

    /**
     * 相同为1，不同为0
     *
     * @param aString 字符串
     * @param bString 字符串
     * @return 返回值
     */
    private static Integer getdealStr(String aString, String bString) {
        String onePart = "";
        for (int i = 0; i < aString.length(); i++) {
            char ch = aString.charAt(i);
            char ch1 = bString.charAt(i);
            if (ch == ch1) {
                onePart = onePart + "1";
            } else {
                onePart = onePart + "0";
            }
        }
        return Integer.valueOf(onePart, 2);
    }

    /**
     * 左补位,右对齐
     *
     * @param oriStr 原字符串
     * @param len    目标字符串长度
     * @param alexin 补位字符
     * @return 目标字符串
     */
    public static String padLeft(String oriStr, int len, char alexin) {
        String str = "";
        int strlen = oriStr.length();
        if (strlen < len) {
            for (int i = 0; i < len - strlen; i++) {
                str = str + alexin;
            }
        }
        str = str + oriStr;
        return str;
    }

    /**
     * 把数组形式转换为字符串的形式
     *
     * @param ipaddrs ipv6地址段信息
     * @return 返回值
     */
    public static String initaddr(List<String> ipaddrs) {
        String iparray = "";
        for (int i = 0; i < ipaddrs.size(); i++) {
            iparray += ipaddrs.get(i);
        }
        if (iparray.charAt(iparray.length() - 1) == ':') {
            iparray = iparray.substring(0, iparray.length() - 1);
        }
        return ipV6UnifiedFormat(iparray);
    }


    /**
     * 获取IPV6开始地址与结束地址中所有的ip集
     *
     * @param startIp 开始ip
     * @param endIp   结束ip
     * @return 返回值
     */
    public List<String> getAllIp(String startIp, String endIp) {
        List<String> list = new ArrayList<String>();

        if (StringUtils.isEmpty(startIp) || StringUtils.isEmpty(endIp)) {
            return list;
        }
        BigInteger startIpNum = ipv6toInt(startIp);
        BigInteger endIpNum = ipv6toInt(endIp);

        if (startIpNum == null || endIpNum == null) {
            return list;
        }

        if (startIpNum.compareTo(endIpNum) < 0) {
            BigInteger num = endIpNum.subtract(startIpNum).add(new BigInteger("1"));
            if (new BigInteger("2").compareTo(num) == 0) {
                list.add(startIp);
                list.add(endIp);
            } else {
                int numInt = num.intValue();
                for (int i = 0; i < numInt; i++) {
                    String ip = int2ipv6(startIpNum.add(new BigInteger(i + "")));
                    list.add(ip);
                }
            }
        } else if (startIp.equals(endIp)) {
            list.add(startIp);
        }
        return list;
    }

    /**
     * 判断ip是否在ip段内
     *
     * @param ipv6
     * @param ipMask
     * @return
     */
    public static boolean isInIpV6Mask(String ipv6, String ipMask) {
        ipv6 = ipV6UnifiedFormat(ipv6);
        String[] split = ipMask.split("\\/");
        String ipv6Start = ipV6Start(split[0], split[1]);
        String ipv6End = ipV6End(split[0], split[1]);
        BigInteger startInt = ipv6toInt(ipv6Start);
        BigInteger endInt = ipv6toInt(ipv6End);
        BigInteger valueInt = ipv6toInt(ipv6);
        if (null != valueInt && valueInt.compareTo(startInt) > -1 && valueInt.compareTo(endInt) < 1) {
            return true;
        }
        return false;
    }

    public static String[] ipV6Mask2Seg(String ipMask) {
        String[] ipv6Seg = new String[2];

        String[] split = ipMask.split("\\/");
        String ipv6Start = ipV6Start(split[0], split[1]);
        String ipv6End = ipV6End(split[0], split[1]);

        ipv6Seg[0] = ipv6Start;
        ipv6Seg[1] = ipv6End;

        return ipv6Seg;
    }

    /**
     * 判断ip是否在IP范围内
     * @param ipv6
     * @param ipv6Start
     * @param ipv6End
     * @return
     */
    public static boolean isInIpV6Range(String ipv6,String ipv6Start,String ipv6End){
        ipv6 = ipV6UnifiedFormat(ipv6);
        BigInteger startInt = ipv6toInt(ipv6Start);
        BigInteger endInt = ipv6toInt(ipv6End);
        BigInteger valueInt = ipv6toInt(ipv6);
        if (null != valueInt && valueInt.compareTo(startInt) > -1 && valueInt.compareTo(endInt) < 1) {
            return true;
        }
        return false;
    }

    public static void main(String[] args) {

    }
}
