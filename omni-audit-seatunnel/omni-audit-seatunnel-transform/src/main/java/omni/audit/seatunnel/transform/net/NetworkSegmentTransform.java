package omni.audit.seatunnel.transform.net;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import omni.audit.label.handle.LabelHandle;
import omni.audit.network.segment.handle.entity.NetworkDomain;
import omni.audit.network.segment.handle.entity.Position;
import omni.audit.network.segment.handle.processors.NetWorkHandle;
import org.apache.seatunnel.api.common.JobContext;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.schema.event.SchemaChangeEvent;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;
import org.apache.seatunnel.transform.common.ErrorHandleWay;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class NetworkSegmentTransform extends AbstractCatalogSupportMapTransform {
    public static final String PLUGIN_NAME = "NetworkSegment";
    private transient NetWorkHandle netWorkHandle;
    private final Map<String, String> netParam;
    private int netSrcIpIndex = -1;
    private int netDstIpIndex = -1;
    private int visitDomainsIndex = -1;
    private int deployDomainsIndex = -1;

    public NetworkSegmentTransform(@NonNull CatalogTable catalogTable,Map<String, String> netParam) {
        super(catalogTable);
        this.netParam = netParam;
        SeaTunnelRowType seaTunnelRowType = catalogTable.getTableSchema().toPhysicalRowDataType();
        initOutputFields(seaTunnelRowType);
    }

    @Override
    public void open() {
        netWorkHandle = new NetWorkHandle();
    }

    private void tryOpen() {
        if (netWorkHandle == null) {
            open();
        }
    }

    private void initOutputFields(
            SeaTunnelRowType inputRowType) {
        List<String> fieldNames = new ArrayList<>(Arrays.asList(inputRowType.getFieldNames()));
        for (int i = 0; i < fieldNames.size(); i++) {
            if (fieldNames.get(i).equals(netParam.get("netSrcIp"))) {
                netSrcIpIndex = i;
            }
            if (fieldNames.get(i).equals(netParam.get("netDstIp"))) {
                netDstIpIndex = i;
            }
            if (fieldNames.get(i).equals(netParam.get("visitDomains"))) {
                visitDomainsIndex = i;
            }
            if (fieldNames.get(i).equals(netParam.get("deployDomains"))) {
                deployDomainsIndex = i;
            }

        }
    }


    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        if (netSrcIpIndex == -1 || netDstIpIndex == -1 || visitDomainsIndex == -1 || deployDomainsIndex == -1) {
            return seaTunnelRow;
        }
        handleNetWork(seaTunnelRow,netSrcIpIndex,visitDomainsIndex);
        handleNetWork(seaTunnelRow,netDstIpIndex,deployDomainsIndex);
        return seaTunnelRow;
    }

    private void handleNetWork(SeaTunnelRow seaTunnelRow,Integer IpIndex,Integer domainsIndex){
        tryOpen();
        Map<String, Object> ipRes = netWorkHandle.handle((String) seaTunnelRow.getField(IpIndex));
        if (ipRes != null && ipRes.get("network") != null ) {
            List<NetworkDomain> network = (List<NetworkDomain>) ipRes.get("network");
            Integer[] array = network.stream().map(s->{
                String id = s.getId();
                if (id.equals("局域网-其他")){
                    return 1;
                }else if (id.equals("局域网-局域网")){
                    return 2;
                }else if (id.equals("互联网-局域网")){
                    return 3;
                }else if (id.equals("互联网-互联网")){
                    return 4;
                }else if (id.equals("互联网-其他")){
                    return 5;
                }else if (id.equals("局域网-其他")){
                    return 6;
                }else {
                    return 0;
                }
            }).toArray(Integer[]::new);
            //TODO 地域暂时不做处理
            seaTunnelRow.setField(domainsIndex, array);
        }
    }

    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }

}
