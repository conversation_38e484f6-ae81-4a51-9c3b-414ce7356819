package omni.audit.seatunnel.transform.filter;

import com.google.auto.service.AutoService;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

@AutoService(Factory.class)
public class FilterTransformFactory implements TableTransformFactory {
    @Override
    public String factoryIdentifier() {
        return FilterTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return () -> new FilterMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}
