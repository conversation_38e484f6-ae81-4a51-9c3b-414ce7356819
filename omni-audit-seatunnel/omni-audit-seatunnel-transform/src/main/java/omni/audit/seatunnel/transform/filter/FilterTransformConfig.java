package omni.audit.seatunnel.transform.filter;

import lombok.Getter;
import lombok.Setter;
import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;

import java.io.Serializable;
import java.util.Optional;

@Setter
@Getter
public class FilterTransformConfig implements Serializable {

    private String id;

    public static final Option<String> EVENT_ID =
            Options.key("id").stringType().noDefaultValue().withDescription("Specify the field append between input and output");

    public static FilterTransformConfig of(ReadonlyConfig config) {
        FilterTransformConfig filterTransformConfig = new FilterTransformConfig();
        Optional<String> optional = config.getOptional(EVENT_ID);
        optional.ifPresent(filterTransformConfig::setId);
        return filterTransformConfig;
    }
}
