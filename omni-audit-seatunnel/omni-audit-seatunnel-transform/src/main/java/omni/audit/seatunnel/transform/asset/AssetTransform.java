package omni.audit.seatunnel.transform.asset;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.quanzhi.omni.audit.parse.SqlParseProcessor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.processors.AssetHandlerInit;
import omni.audit.seatunnel.transform.common.AbstractSupportAllMapTransform;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;

import java.util.*;

@Slf4j
public class AssetTransform extends AbstractSupportAllMapTransform {
    public static final String PLUGIN_NAME = "Asset";
    private transient  AssetHandlerInit assetHandlerInit;

    private List<String> fieldNames;
    private final Map<String,Object> assetIdParam;
    private final Map<String,String> assetTimeParam;
    private final List<Map<String, String>>  assetLevelParam;
    private final List<Map<String, String>>  assetAssignmentParam;
    private final List<String> handleNameList;
    private final Map<String,Map<Integer,String>> fieldNameToIndexMap = new HashMap<>();

    public AssetTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        super(catalogTable, readonlyConfig.get(AssetTransformConfig.ASSET_MAP_PARAM));
        Map<String, Object> assetIdParam = readonlyConfig.get(AssetTransformConfig.ASSET_ID_PARAM);
        Map<String, String> assetTimeParam = readonlyConfig.get(AssetTransformConfig.ASSET_TIME_PARAM);
        List<Map<String, String>> assetLevelParam = readonlyConfig.get(AssetTransformConfig.ASSET_LEVEL_PARAM);
        List<Map<String, String>> assetAssignmentParam = readonlyConfig.get(AssetTransformConfig.ASSET_ASSIGNMENT_PARAM);
        List<String> handleNameList = readonlyConfig.get(AssetTransformConfig.ASSET_HANDLE);
        this.assetIdParam = assetIdParam;
        this.assetTimeParam = assetTimeParam;
        this.assetLevelParam = assetLevelParam;
        this.assetAssignmentParam = assetAssignmentParam;
        this.handleNameList = handleNameList;

    }

    private void initOutputFields(
            SeaTunnelRowType inputRowType) {
        List<String> fieldNames = new ArrayList<>(Arrays.asList(inputRowType.getFieldNames()));
        this.fieldNames = fieldNames;
    }

    @Override
    public void open() {
        this.assetHandlerInit = new AssetHandlerInit(handleNameList);
    }

    private void tryOpen() {
        if (assetHandlerInit == null) {
            open();
        }
    }


    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        Map<String, Object> eventMap = convertRowToMap(seaTunnelRow);

        HandleMap handleMap = new HandleMap();
        handleMap.setEventMap(eventMap);
        handleMap.setAssetIdParam(assetIdParam);
        handleMap.setAssetTimeParam(assetTimeParam);
        handleMap.setAssetLevelParam(assetLevelParam);
        handleMap.setAssetAssignmentParam(assetAssignmentParam);
        assetHandlerInit.handle(handleMap);
        convertMapToRow(seaTunnelRow,handleMap.getEventMap());


        return seaTunnelRow;
    }


    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }

}
