package omni.audit.seatunnel.transform.sql;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.omni.audit.parse.SqlParseProcessor;
import com.quanzhi.sqlparser.context.TableInfo;
import com.quanzhi.sqlparser.view.SqlParserResult;
import lombok.NonNull;
import omni.audit.seatunnel.transform.common.AbstractFieldNameSupportMapTransform;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SqlParseTransform extends AbstractFieldNameSupportMapTransform {
    public static String PLUGIN_NAME = "SqlParse";
    private final SqlParseTransformConfig sqlParseTransformConfig;

    private transient SqlParseProcessor sqlParseProcessor;

    private final Map<String, Integer> tableSchemeIndexMap;

    public SqlParseTransform(SqlParseTransformConfig sqlParseTransformConfig, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.sqlParseTransformConfig = sqlParseTransformConfig;
        SchemaParser schemaParser = new SchemaParser();
        LinkedHashMap<String, String> tableScheme = sqlParseTransformConfig.getTableScheme();
        TableSchema parse = schemaParser.parse(tableScheme);
        String[] fieldNames = parse.getFieldNames();
        tableSchemeIndexMap = new HashMap<>();
        for (int i = 0; i < fieldNames.length; i++) {
            tableSchemeIndexMap.put(fieldNames[i], i);
        }
    }

    @Override
    public void open() {
        this.sqlParseProcessor = new SqlParseProcessor();
    }

    private void tryOpen() {
        if (sqlParseProcessor == null) {
            open();
        }
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        String sql = String.valueOf(getValueByFieldName(seaTunnelRow, sqlParseTransformConfig.getSql()));
        String configTables = sqlParseTransformConfig.getTables();
        String dbType = String.valueOf(getValueByFieldName(seaTunnelRow, sqlParseTransformConfig.getDbType()));
        LinkedHashMap<String, String> tableScheme = sqlParseTransformConfig.getTableScheme();
        LinkedHashMap<String, String> tableInfo = sqlParseTransformConfig.getTableInfo();
        SqlParserResult sqlParserResult = sqlParseProcessor.process(sql,dbType);
        if (sqlParserResult != null && sqlParserResult.getAllTableInfo() != null && sqlParserResult.getAllTableInfo().getTables() != null && !sqlParserResult.getAllTableInfo().getTables().isEmpty()) {
            Map<Integer,SeaTunnelRow> tables = new HashMap<>();
            int size = sqlParserResult.getAllTableInfo().getTables().size();
            for (int j = 0; j < size; j++) {
                TableInfo info = sqlParserResult.getAllTableInfo().getTables().get(j);
                ObjectMapper mapper = new ObjectMapper();
                Map<String, Object> infoMap = mapper.convertValue(info, Map.class);
                if(info.getAllColumnNames() != null && !info.getAllColumnNames().isEmpty()){
                    List<String> columns = info.getAllColumnNames().stream().filter(i -> i != null && !"*".equals(i)).collect(Collectors.toList());
                    infoMap.put("columns", columns);
                }
                //TODO 处理sqlParserResult
                Object[] objects = new Object[tableScheme.size()];
                tableInfo.forEach((k, v) -> {
                    Integer index = tableSchemeIndexMap.get(k);
                    objects[index] = infoMap.get(v);
                });
                SeaTunnelRow newRow = new SeaTunnelRow(objects);
                newRow.setRowKind(seaTunnelRow.getRowKind());
                newRow.setTableId(seaTunnelRow.getTableId());
                tables.put(j, newRow);
            }
            Integer indexByFieldName = getIndexByFieldName(seaTunnelRow, configTables);
            seaTunnelRow.setField(indexByFieldName,tables);
        }
        return seaTunnelRow;
    }

    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema().copy();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }

    @Override
    public void close() {
    }
}
