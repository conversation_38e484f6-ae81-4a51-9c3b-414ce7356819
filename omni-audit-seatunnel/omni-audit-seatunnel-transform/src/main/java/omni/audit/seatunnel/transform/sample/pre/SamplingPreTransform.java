package omni.audit.seatunnel.transform.sample.pre;

import lombok.NonNull;
import omni.audit.seatunnel.transform.common.AbstractFieldNameSupportMapTransform;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;

/**
 * <AUTHOR>
 * create at 2025/5/20 13:56
 * @description:
 **/
public class SamplingPreTransform extends AbstractFieldNameSupportMapTransform {

    public static final String PLUGIN_NAME = "SamplingPre";


    public SamplingPreTransform(@NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        // TODO: 关联出资产对应的已采到的样例




        return seaTunnelRow;
    }

    @Override
    protected TableSchema transformTableSchema() {
        return null;
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return null;
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}