package omni.audit.seatunnel.transform.weakness;

import com.quanzhi.audit.weakness.constant.WeaknessConstant;
import com.quanzhi.audit.weakness.service.WeaknessService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import omni.audit.seatunnel.transform.common.AbstractFieldNameSupportMapTransform;
import omni.audit.seatunnel.transform.common.SchemaParser;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:15
 * @description:
 **/
@Slf4j
public class WeaknessTransform extends AbstractFieldNameSupportMapTransform {
    public static final String PLUGIN_NAME = "Weakness";
    private final WeaknessTransformConfig config;
    private final Map<String, Integer> weaknessSchemeIndexMap;
    private transient WeaknessService weaknessService;

    public WeaknessTransform(WeaknessTransformConfig weaknessTransformConfig, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = weaknessTransformConfig;

        SchemaParser schemaParser = new SchemaParser();
        TableSchema tableSchema = schemaParser.parse(config.getWeaknessScheme());
        String[] fieldNames = tableSchema.getFieldNames();
        this.weaknessSchemeIndexMap = new HashMap<>();
        for (int i = 0; i < fieldNames.length; i++) {
            weaknessSchemeIndexMap.put(fieldNames[i], i);
        }

        LinkedHashMap<String, String> fieldMap = weaknessTransformConfig.getFieldMap();
        config.getWeaknessConfig().put(WeaknessConstant.FIELD_MAP, fieldMap);

        LinkedHashMap<String, String> outputFieldMap = weaknessTransformConfig.getOutputFieldMap();
        config.getWeaknessConfig().put(WeaknessConstant.OUTPUT_FIELD_MAP, outputFieldMap);
    }

    @Override
    public void open() {
        this.weaknessService = new WeaknessService(config.getJdbcConfig(), config.getWeaknessConfig());
    }

    private void tryOpen() {
        if (weaknessService == null) {
            open();
        }
    }


    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        Map<String, Object> map = convertRowToMap(seaTunnelRow);
        List<Map<String, Object>> weakness = weaknessService.process(map);
        if (!weakness.isEmpty()) {
            Map<Integer,SeaTunnelRow> weaknessSeatunnelRow=new HashMap<>();
            for (int i = 0; i <weakness.size() ; i++) {
                Map<String, Object> singleWeakness = weakness.get(i);
                Object[] objects = new Object[weaknessSchemeIndexMap.size()];
                weaknessSchemeIndexMap.forEach((fieldName,fieldIndex)->{
                    objects[fieldIndex]=singleWeakness.get(fieldName);
                });
                SeaTunnelRow row=new SeaTunnelRow(objects);
                row.setRowKind(seaTunnelRow.getRowKind());
                row.setTableId(seaTunnelRow.getTableId());
                weaknessSeatunnelRow.put(i,row);
            }

            Integer index = getIndexByFieldName(seaTunnelRow, "weaknesses");
            seaTunnelRow.setField(index, weaknessSeatunnelRow);
        }
        return seaTunnelRow;
    }

    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema().copy();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}