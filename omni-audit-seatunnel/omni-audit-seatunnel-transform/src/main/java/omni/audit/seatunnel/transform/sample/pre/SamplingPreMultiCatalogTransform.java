package omni.audit.seatunnel.transform.sample.pre;

import org.apache.seatunnel.api.configuration.ReadonlyConfig;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.transform.SeaTunnelTransform;
import org.apache.seatunnel.transform.common.AbstractMultiCatalogMapTransform;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/5/20 12:59
 * @description:
 **/
public class SamplingPreMultiCatalogTransform extends AbstractMultiCatalogMapTransform {


    public SamplingPreMultiCatalogTransform(List<CatalogTable> inputCatalogTables, ReadonlyConfig config) {
        super(inputCatalogTables, config);
    }

    @Override
    protected SeaTunnelTransform<SeaTunnelRow> buildTransform(CatalogTable catalogTable, ReadonlyConfig readonlyConfig) {
        return null;
    }

    @Override
    public String getPluginName() {
        return SamplingPreTransform.PLUGIN_NAME;
    }
}