package omni.audit.seatunnel.transform.common;

import lombok.NonNull;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.transform.common.AbstractCatalogSupportMapTransform;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/5/13 10:20
 * @description:
 **/
public abstract class AbstractFieldNameSupportMapTransform extends AbstractCatalogSupportMapTransform implements Serializable {

    /**
     * key:fieldName
     * value:fieldIndex
     */
    private final Map<String, Integer> fieldNameToIndexMap;

    public AbstractFieldNameSupportMapTransform(@NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.fieldNameToIndexMap = new HashMap<>();
        TableSchema schema = inputCatalogTable.getTableSchema();
        String[] fieldNames = schema.getFieldNames();
        for (int i = 0; i < fieldNames.length; i++) {
            fieldNameToIndexMap.put(fieldNames[i], i);
        }
    }
    /**
     * 根据字段名从 SeaTunnelRow 中获取对应的值
     *
     * @param row       SeaTunnelRow 对象
     * @param fieldName 字段名
     * @return 字段对应的值
     */
    public Object getValueByFieldName(SeaTunnelRow row, String fieldName) {
        Integer index = fieldNameToIndexMap.get(fieldName);
        if (index != null) {
            return row.getField(index);
        }
        throw new IllegalArgumentException("Field not found: " + fieldName);
    }

    public Integer getIndexByFieldName(SeaTunnelRow row, String fieldName) {
        Integer index = fieldNameToIndexMap.get(fieldName);
        return index;
    }

    public Map<String,Object> convertRowToMap(SeaTunnelRow row){
        Map<String,Object> map = new HashMap<>();
        TableSchema schema = inputCatalogTable.getTableSchema();
        String[] fieldNames = schema.getFieldNames();
        for (int i = 0; i < fieldNames.length; i++) {
            map.put(fieldNames[i],row.getField(i));
        }
        return map;
    }
}
