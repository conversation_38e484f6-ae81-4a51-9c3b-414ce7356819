package omni.audit.seatunnel.transform.sample.pre;

import com.google.auto.service.AutoService;
import omni.audit.seatunnel.transform.sample.storage.SamplingStorageMultiCatalogTransform;
import omni.audit.seatunnel.transform.sample.storage.SamplingStorageTransform;
import org.apache.seatunnel.api.configuration.util.OptionRule;
import org.apache.seatunnel.api.table.connector.TableTransform;
import org.apache.seatunnel.api.table.factory.Factory;
import org.apache.seatunnel.api.table.factory.TableTransformFactory;
import org.apache.seatunnel.api.table.factory.TableTransformFactoryContext;

/**
 * <AUTHOR>
 * create at 2025/5/13 11:33
 * @description:
 **/
@AutoService(Factory.class)
public class SamplingPreTransformFactory implements TableTransformFactory {


    @Override
    public String factoryIdentifier() {
        return SamplingPreTransform.PLUGIN_NAME;
    }

    @Override
    public OptionRule optionRule() {
        return OptionRule.builder().build();
    }

    @Override
    public TableTransform createTransform(TableTransformFactoryContext context) {
        return () -> new SamplingPreMultiCatalogTransform(context.getCatalogTables(), context.getOptions());
    }
}