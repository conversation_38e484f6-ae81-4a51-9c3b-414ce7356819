package omni.audit.seatunnel.transform.filter;

import com.quanzhi.omni.audit.processors.FilterProcessor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import omni.audit.seatunnel.transform.common.AbstractFieldNameSupportMapTransform;
import org.apache.seatunnel.api.table.catalog.CatalogTable;
import org.apache.seatunnel.api.table.catalog.TableIdentifier;
import org.apache.seatunnel.api.table.catalog.TableSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;

import java.util.Map;

@Slf4j
public class FilterTransform extends AbstractFieldNameSupportMapTransform {
    public static final String PLUGIN_NAME = "EventFilter";
    private final FilterTransformConfig config;

    private transient FilterProcessor filterProcessor;

    public FilterTransform(FilterTransformConfig filterTransformConfig, @NonNull CatalogTable inputCatalogTable) {
        super(inputCatalogTable);
        this.config = filterTransformConfig;
    }

    @Override
    public void open() {
        try {
            Class.forName("org.postgresql.Driver");
        } catch (ClassNotFoundException e) {
        }
        this.filterProcessor = new FilterProcessor("*********************************************","postgres","password123","public");
    }

    private void tryOpen() {
        if (filterProcessor == null) {
            open();
        }
    }

    @Override
    protected SeaTunnelRow transformRow(SeaTunnelRow seaTunnelRow) {
        tryOpen();
        Map<String, Object> map = convertRowToMap(seaTunnelRow);
        boolean execute = filterProcessor.execute(map);
        if(!execute){
            Object id = getValueByFieldName(seaTunnelRow, config.getId());
//            log.warn("event id:{} is filtered",id);
            // 被过滤，返回null下游就不会处理了
            return null;
        }
        return seaTunnelRow;
    }

    @Override
    protected TableSchema transformTableSchema() {
        return inputCatalogTable.getTableSchema().copy();
    }

    @Override
    protected TableIdentifier transformTableIdentifier() {
        return inputCatalogTable.getTableId().copy();
    }

    @Override
    public String getPluginName() {
        return PLUGIN_NAME;
    }
}
