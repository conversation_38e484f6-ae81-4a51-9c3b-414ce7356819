env {
  parallelism = 1
  job.mode = "STREAMING"
}

source {
  Kafka {

    topic = "dbEvent"
    bootstrap.servers = "*************:9094"
    kafka.config = {
      max.poll.records = 100
      auto.offset.reset = "earliest"
    }
    plugin_output = "db_event"
    format = json
    schema = {
      fields = {
        meta = {
          tm = bigint
        }
        net = {
          src_ip = string
          src_port = int
          dst_ip = string
          dst_port = int
          flow_source = string
        }
        mac = {
          mac = string
        }
        unique_id = {
          event_id = string
        }
        req = {
          db_name = string
          db_user = string
          sql = string
        }
        rsp = {
          status = int
          start_time = bigint
          close_time = bigint
          row_count = int
          result = string
        }

      }
    }
  }
}

transform {

  JsonPath {
    plugin_input = "db_event"
    plugin_output = "db_event_flatten"
    columns = [
    {
      "src_field" = "meta"
      "path" = "$.meta.tm"
      "dest_field" = "timestamp"
        "dest_type" = "bigint"
    },
    {
          "src_field" = "net"
          "path" = "$.net.src_ip"
          "dest_field" = "netSrcIp"
          "dest_type" = "string"
        },
        {
          "src_field" = "net"
          "path" = "$.net.src_port"
          "dest_field" = "netSrcPort"
          "dest_type" = "int"
        },
        {
          "src_field" = "net"
          "path" = "$.net.dst_ip"
          "dest_field" = "netDstIp"
          "dest_type" = "string"
        },
        {
          "src_field" = "net"
          "path" = "$.net.dst_port"
          "dest_field" = "netDstPort"
          "dest_type" = "int"
        },
       {
                 "src_field" = "net"
                 "path" = "$.net.flow_source"
                 "dest_field" = "netFlowSource"
                 "dest_type" = "string"
               },
               {
                 "src_field" = "mac"
                 "path" = "$.mac.mac"
                 "dest_field" = "mac"
                 "dest_type" = "string"
               },
               {
                 "src_field" = "unique_id"
                 "path" = "$.unique_id.event_id"
                 "dest_field" = "eventId"
                 "dest_type" = "string"
               },
               {
                 "src_field" = "req"
                 "path" = "$.req.db_name"
                 "dest_field" = "reqDbName"
                 "dest_type" = "string"
               },
               {
                 "src_field" = "req"
                 "path" = "$.req.db_user"
                 "dest_field" = "reqDbUser"
                 "dest_type" = "string"
               },
               {
                 "src_field" = "req"
                 "path" = "$.req.sql"
                 "dest_field" = "reqSql"
                 "dest_type" = "string"
               },
               {
                 "src_field" = "rsp"
                 "path" = "$.rsp.status"
                 "dest_field" = "rspStatus"
                 "dest_type" = "int"
               },
               {
                 "src_field" = "rsp"
                 "path" = "$.rsp.start_time"
                 "dest_field" = "rspStartTime"
                 "dest_type" = "bigint"
               },
               {
                 "src_field" = "rsp"
                 "path" = "$.rsp.close_time"
                 "dest_field" = "rspCloseTime"
                 "dest_type" = "bigint"
                 },
                 {
                   "src_field" = "rsp"
                   "path" = "$.rsp.row_count"
                   "dest_field" = "rspRowCount"
                   "dest_type" = "int"
                 },
                 {
                   "src_field" = "rsp"
                   "path" = "$.rsp.result"
                   "dest_field" = "rspResult"
                   "dest_type" = "string"
                 }
    ]
  }

}

sink {
  console {
    plugin_input = "db_event_flatten"
  }
}
