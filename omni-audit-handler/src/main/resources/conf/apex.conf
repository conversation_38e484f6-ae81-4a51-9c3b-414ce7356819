env {
  parallelism = 1
  job.name = "oad-handler"
}

source {
  kafka {
    parallelism = 1
    topic = "DBEvent"
    bootstrap.servers = "*************:9094"
    kafka.config = {
      max.poll.records = 100
      auto.offset.reset = "earliest"
    }
    plugin_output = "db_event"
    format = json
    schema = {
      fields = {
        meta = {
          tm = bigint
          type = string
          app_name = string
          server_version = string
        }
        net = {
          src_ip = string
          src_port = int
          dst_ip = string
          dst_port = int
          flow_source = string
        }
        mac = {
          mac = string
        }
        unique_id = {
          event_id = string
        }
        req = {
          db_name = string
          db_user = string
          db_password = string
          sql_cmd_type = string
          sql = string
        }
        rsp = {
          status = int
          start_time = bigint
          close_time = bigint
          row_count = int
          result = string
        }

      }
    }
  }
}

transform {

}

sink {
  console {

  }
}