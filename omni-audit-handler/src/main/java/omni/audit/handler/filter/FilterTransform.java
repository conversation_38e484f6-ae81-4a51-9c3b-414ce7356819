package omni.audit.handler.filter;

import com.quanzhi.omni.audit.processors.FilterProcessor;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.api.type.ApexRow;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class FilterTransform implements ApexTransform<ApexRow> {
    public static final String PLUGIN_NAME = "EventFilter";
    private final FilterTransformConfig config;

    private transient FilterProcessor filterProcessor;

    public FilterTransform(FilterTransformConfig filterTransformConfig) {
        this.config = filterTransformConfig;
    }

    @Override
    public void open() {
        try {
            Class.forName("org.postgresql.Driver");
        } catch (ClassNotFoundException e) {
        }
        this.filterProcessor = new FilterProcessor("**********************************************","postgres","uO0LpGbeQy2T0DWR","public");
    }

    private void tryOpen() {
        if (filterProcessor == null) {
            open();
        }
    }

    @Override
    public ApexRow transform(ApexRow row) {
        tryOpen();
        Map<String, Object> map = new HashMap<>();
        boolean execute = filterProcessor.execute(map);
        if(!execute){
            Object id = row.get(config.getId(),String.class);
//            log.warn("event id:{} is filtered",id);
            // 被过滤，返回null下游就不会处理了
            return null;
        }
        return row;
    }
}
