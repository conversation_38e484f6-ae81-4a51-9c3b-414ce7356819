create table if not exists event_log
(
    timestamp      UInt32  CODEC(Delta, ZSTD(1)),
    id             String  CODEC(ZSTD(1)),
    eventTypes     Array(UInt8)  CODEC(T64, ZSTD(1)),
    net_dstIp_v4   IPv4,
    net_dstIp_v6   IPv6,
    net_dstPort    UInt16  CODEC(T64, ZSTD(1)),
    net_srcIp_v4   IPv4,
    net_srcIp_v6   IPv6,
    net_srcPort    UInt16  CODEC(T64, ZSTD(1)),
    accessDomains  Array(UInt8) CODEC(T64, ZSTD(1)),
    account        LowCardinality(String),
    accountType    UInt8 CODEC(T64, ZSTD(1)),
    srvAddress     LowCardinality(String),
    srvName        LowCardinality(String),
    srvType        UInt8 CODEC(T64, ZSTD(1)),
    srvLevel       UInt8 CODEC(T64, ZSTD(1)),
    dbName         LowCardinality(String),
    dbType         UInt8 CODEC(T64, ZSTD(1)),
    dbLevel        UInt8 CODEC(T64, ZSTD(1)),
    tableName      LowCardinality(String),
    tableLevel     UInt8 CODEC(T64, ZSTD(1)),
    optionMethod   UInt8 CODEC(T64, ZSTD(1)),
    deployDomains  Array(UInt8) CODEC(T64, ZSTD(1)),
    reqDataLabelIds Array(UInt16) CODEC(T64, ZSTD(1)),
    rspDataLabelIds Array(UInt16) CODEC(T64, ZSTD(1)),
    rspTime        UInt32 CODEC(T64, ZSTD(1)),
    rspStatus      LowCardinality(String)
    -- INDEX dateIndex date TYPE set(0) GRANULARITY 10000,
    -- INDEX apiUrlIndex apiUrl TYPE tokenbf_v1(20480, 2, 0) GRANULARITY 4,
    -- INDEX idx_rspContentLength rspContentLength TYPE minmax GRANULARITY 4,
    -- INDEX classificationsBfIndex classifications TYPE bloom_filter(0.008) GRANULARITY 4,
    -- PROJECTION apiUrl_proj ( SELECT apiUrl,timestamp,id ORDER BY apiUrl,timestamp )
    )
    engine = MergeTree()
    PARTITION BY toDate(timestamp)
    ORDER BY (timestamp, id, sipHash64(id))
    SAMPLE BY sipHash64(id)
    SETTINGS index_granularity = 8192,max_suspicious_broken_parts=1000;
