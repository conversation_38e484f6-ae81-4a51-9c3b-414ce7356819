create table public.dc_table
(
    id          bigint generated by default as identity
        primary key,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now(),
    name        varchar,
    schema_id   bigint,
    database_id bigint,
    data_size   bigint,
    comment     text,
    manual      int2                  default 0,
    remark      varchar,
    encoding    varchar,
    source_id   bigint,
    column_count bigint,
    level_id     bigint,
    type        varchar
);

create table public.dc_database
(
    id         bigint generated by default as identity
        primary key,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone,
    name       varchar                                not null,
    source_id  bigint                                 not null,
    comment     text,
    manual      int2                 default 0,
    remark      varchar,
    data_size  bigint,
    type        varchar,
    hash       varchar                                not null unique,
    schema_count bigint,
    table_count bigint,
    column_count bigint,
    sys_db      bool
);
create table public.dc_column
(
    id          bigint generated by default as identity
        constraint dc_field_pkey
            primary key,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone,
    name        varchar,
    comment     text,
    ts_comment  tsvector,
    remark      text,
    ts_remark   tsvector,
    type        varchar,
    data_size   bigint,
    level_id    bigint,
    labels      bigint[],
    table_id    bigint,
    source_id   bigint,
    manual      int2                  default 0,
    database_id bigint,
    schema_id   bigint,
    encrypt     boolean                  default false,
    encoding    varchar,
    samples     text,
    tasks       bigint[]
);
create table public.dc_schema
(
    id          bigint generated by default as identity
        primary key,
    created_at  timestamp with time zone default now() not null,
    updated_at  timestamp with time zone default now() not null,
    name        varchar                                not null,
    database_id bigint                                 not null,
    comment     text,
    manual      int2                  default 0,
    type        varchar,
    remark      varchar,
    source_id   bigint,
    table_count bigint,
    column_count bigint,
    data_size bigint
);