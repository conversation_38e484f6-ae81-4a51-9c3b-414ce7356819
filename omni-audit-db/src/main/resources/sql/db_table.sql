CREATE TABLE IF NOT EXISTS db_table
(
    id               SERIAL PRIMARY KEY,
    table_id            VARCHAR,
    name             VA<PERSON>HAR     NOT NULL,
    db_type          VARCHAR,
    db_version       VARCHAR,
    sensi_level      VARCHAR,
    risk_level       VARCHAR,
    rel_srv_addr     VARCHAR,
    rel_srv_name     VARCHAR,
    column_cnt       INT         DEFAULT 0,
    sensi_column_cnt INT         DEFAULT 0,
    access_domains   TEXT[],
    deploy_domains   TEXT[],
    req_labels       INT[],
    rsp_labels       INT[],
    status           VARCHAR,
    risk_cnt         INT                  DEFAULT 0,
    risk_events      TEXT[],
    biz_system       VARCHAR,
    first_at         BIGINT      ,
    last_at          BIGINT      ,
    created_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at       TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')

);