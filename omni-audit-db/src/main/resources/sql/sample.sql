CREATE TABLE IF NOT EXISTS sample (
    -- 事件ID，设置为主键
    event_id VARCHAR(255) PRIMARY KEY,
    -- 请求时间（时间戳），精确到秒
    timestamp BIGINT,
    -- 源IP
    net_src_ip VARCHAR(255),
    -- 源端口
    net_src_port INTEGER,
    -- 目的IP
    net_dst_ip VARCHAR(255),
    -- 目的端口
    net_dst_port INTEGER,
    -- 流量来源
    net_flow_source VARCHAR(255),
    -- MAC 地址
    mac VARCHAR(255),
    -- 数据库名
    req_db_name VARCHAR(255),
    -- 访问账号
    req_db_user VARCHAR(255),
    --  数据库密码
    req_db_password VARCHAR(255),
    -- 数据库语句
    req_sql TEXT,
    -- 响应状态码
    rsp_status INTEGER,
    -- 开始时间
    rsp_start_time BIGINT,
    -- 结束时间
    rsp_close_time BIGINT,
    -- 数据行数
    rsp_row_count INTEGER,
    -- 返回结果
    rsp_result TEXT,
    -- 服务ID
    srv_id VARCHAR(255),
    -- 数据库ID
    db_id VARCHAR(255),

    -- 事件访问域，使用 TEXT 数组类型存储
    access_domains TEXT[],
    -- 事件部署域，使用 TEXT 数组类型存储
    deploy_domains TEXT[],
    -- 事件请求标签，使用 TEXT 数组类型存储
    req_data_labels TEXT[],
    -- 事件返回标签，使用 TEXT 数组类型存储
    rsp_data_labels TEXT[],
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    update_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );
