CREATE TABLE IF NOT EXISTS risk
(
    id             SERIAL PRIMARY KEY,
    risk_id        INT,
    desc           VA<PERSON>HAR,
    risk_subject   VARCHA<PERSON>,
    rel_db_id      VARCHAR,
    rel_db_type    VARCHAR,
    rel_db_srv     VARCHAR,
    name           VA<PERSON>HA<PERSON>,
    type           VARCHAR,
    level          INT,
    status         VARCHAR,
    access_domains TEXT[],
    deploy_domains TEXT[],
    biz_system     VARCHAR,
    first_at       BIGINT      NOT NULL,
    last_at        BIGINT      NOT NULL,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);