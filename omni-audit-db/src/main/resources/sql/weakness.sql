-- Create the main table for vulnerabilities
CREATE TABLE IF NOT EXISTS weakness
(
    id             SERIAL PRIMARY KEY,
    name           <PERSON><PERSON><PERSON><PERSON>,
    type           VA<PERSON>HA<PERSON>,
    level          INT         NOT NULL,
    state          VARCHAR,
    operation_id   VARCHAR UNIQUE,
    weakness_id    INT,
    srv_id          VARCHAR,
    db_id          VARCHAR,
    table_id       VARCHAR,
    first_at       BIGINT      NOT NULL,
    last_at        BIGINT      NOT NULL,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);