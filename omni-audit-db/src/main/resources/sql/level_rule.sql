-- 创建 label_rule 表
CREATE TABLE IF NOT EXISTS level_rule (
    -- 自增主键
                                          id SERIAL PRIMARY KEY,
    -- 标签类型
                                          level_type VARCHAR(255),
    -- 规则
    compiled_rule TEXT,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );