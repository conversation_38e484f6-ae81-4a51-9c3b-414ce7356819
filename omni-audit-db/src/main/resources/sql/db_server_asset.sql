CREATE TABLE IF NOT EXISTS db_srv
(
    id             SERIAL PRIMARY KEY,
    srv_id       VARCHAR,
    srv_addr       VARCHAR,
    srv_name       VARCHAR,
    srv_type       VARCHAR,
    sensi_level    VARCHAR,
    risk_level     VARCHAR,
    access_domains TEXT[],
    deploy_domains TEXT[],
    req_labels     INT[],
    rsp_labels     INT[],
    status         INT,
    db_cnt         INT          DEFAULT 0,
    table_cnt      INT          DEFAULT 0,
    weakness_cnt   INT          DEFAULT 0,
    weakness_ids   TEXT[],
    risk_cnt       INT,
    risk_ids       TEXT[],
    user_cnt       INT          DEFAULT 0,
    flow_sources   VARCHAR,
    biz_system     VARCHAR,
    first_at       BIGINT     ,
    last_at        BIGINT      ,
    created_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at     TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
);