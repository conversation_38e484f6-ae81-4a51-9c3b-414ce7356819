-- Create the main table for database assets
CREATE TABLE IF NOT EXISTS db_assets (
    id SERIAL PRIMARY KEY,
    db_id VARCHAR,
    db_name VA<PERSON>HA<PERSON>,
    db_version VARCHAR,
    db_type VARCHAR,
    sensi_level VARCHAR,
    risk_level VARCHAR,
    rel_db_srv VARCHAR,
    access_domains TEXT[],
    deploy_domains TEXT[],
    req_labels INT[],
    rsp_labels INT[],
    status INT,
    table_cnt INT  DEFAULT 0,
    weakness_cnt INT DEFAULT 0,
    weakness_ids TEXT[],
    risk_cnt INT DEFAULT 0,
    risk_ids TEXT[],
    user_cnt INT  DEFAULT 0,
    biz_system VARCHAR,
    first_at BIGINT,
    last_at BIGINT ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')
    );