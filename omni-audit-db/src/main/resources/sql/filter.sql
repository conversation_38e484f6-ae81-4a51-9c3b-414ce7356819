CREATE TABLE IF NOT EXISTS public.filter
(
    id          VARCHAR(255) PRIMARY KEY,
    "type"      VARCHAR(50)  NOT NULL,
    title       VARCHAR(255) NOT NULL,
    priority    INTEGER      NOT NULL,
    keep_enable BOOLEAN,
    enable     BOOLEAN               DEFAULT TRUE,
    del_flag    B<PERSON>OL<PERSON><PERSON>               DEFAULT FALSE,
    match_rule  JSONB,
    created_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai'),
    updated_at  TIMESTAMPTZ  NOT NULL DEFAULT (NOW() AT TIME ZONE 'Asia/Shanghai')

);
