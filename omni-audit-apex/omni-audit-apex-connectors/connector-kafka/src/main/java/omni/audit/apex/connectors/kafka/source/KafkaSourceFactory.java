package omni.audit.apex.connectors.kafka.source;

import omni.audit.apex.api.factory.ApexSourceFactory;
import omni.audit.apex.api.factory.ApexSourceFactoryContext;
import omni.audit.apex.api.source.ApexSource;

/**
 * <AUTHOR>
 * @class KafkaSourceFactory
 * @created 2025/6/17 17:41
 * @desc
 **/
public class KafkaSourceFactory implements ApexSourceFactory {

    @Override
    public ApexSource createSource(ApexSourceFactoryContext context) {
        return new KafkaSource(context.getOptions());
    }

    @Override
    public String factoryIdentifier() {
        return "Kafka";
    }
}
