package omni.audit.apex.connectors.kafka.source;

import lombok.Getter;
import omni.audit.apex.api.config.ReadonlyConfig;

import java.util.Properties;

import static omni.audit.apex.connectors.kafka.config.KafkaBaseOptions.BOOTSTRAP_SERVERS;
import static omni.audit.apex.connectors.kafka.config.KafkaSourceOptions.*;

/**
 * <AUTHOR>
 * @class KafkaSourceConfig
 * @created 2025/6/16 17:34
 * @desc
 **/
public class KafkaSourceConfig {

    @Getter
    private final String bootstrap;
    // @Getter private final Map<TablePath, ConsumerMetadata> mapMetadata;
    @Getter
    private final boolean commitOnCheckpoint;
    @Getter
    private final Properties properties;
    @Getter
    private final long discoveryIntervalMillis;
    // @Getter private final MessageFormatErrorHandleWay messageFormatErrorHandleWay;
    @Getter
    private final String consumerGroup;
    @Getter
    private final long pollTimeout;

    public KafkaSourceConfig(ReadonlyConfig readonlyConfig) {
        this.bootstrap = readonlyConfig.get(BOOTSTRAP_SERVERS);
        // this.mapMetadata = createMapConsumerMetadata(readonlyConfig);
        this.commitOnCheckpoint = readonlyConfig.get(COMMIT_ON_CHECKPOINT);
        this.properties = createKafkaProperties(readonlyConfig);
        this.discoveryIntervalMillis = readonlyConfig.get(KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS);
        // this.messageFormatErrorHandleWay = readonlyConfig.get(MESSAGE_FORMAT_ERROR_HANDLE_WAY_OPTION);
        this.pollTimeout = readonlyConfig.get(KEY_POLL_TIMEOUT);
        this.consumerGroup = readonlyConfig.get(CONSUMER_GROUP);
    }

    private Properties createKafkaProperties(ReadonlyConfig readonlyConfig) {
        Properties resultProperties = new Properties();
        readonlyConfig.getOptional(KAFKA_CONFIG).ifPresent(resultProperties::putAll);
        return resultProperties;
    }
}
