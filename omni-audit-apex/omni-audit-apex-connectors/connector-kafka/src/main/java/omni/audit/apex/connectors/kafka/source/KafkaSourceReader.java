package omni.audit.apex.connectors.kafka.source;

import omni.audit.apex.api.source.SourceReader;

import java.util.Collection;

/**
 * <AUTHOR>
 * @class KafkaSourceReader
 * @created 2025/6/16 17:46
 * @desc
 **/
public class KafkaSourceReader implements SourceReader {

    @Override
    public void open() {

    }

    @Override
    public void close() {

    }

    @Override
    public Collection<?> pollNext() {
        return null;
    }
}
