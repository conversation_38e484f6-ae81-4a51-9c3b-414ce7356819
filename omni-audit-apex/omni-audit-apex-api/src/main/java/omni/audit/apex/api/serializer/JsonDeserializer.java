package omni.audit.apex.api.serializer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.type.ApexRow;
import omni.audit.apex.api.type.DefaultApexRow;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @class JsonDeserialization
 * @created 2025/6/23 16:14
 * @desc
 **/
@Slf4j
public class JsonDeserializer implements Serializer {

    private String apexRowClass;

    private final ObjectMapper objectMapper = new ObjectMapper();

    public JsonDeserializer() {
        // TODO 祁灵 2025/6/23 17:04:
    }

    @Override
    public byte[] serialize(ApexRow row) {
        if (row instanceof DefaultApexRow) {
            DefaultApexRow defaultApexRow = (DefaultApexRow) row;
            try {
                return objectMapper.writeValueAsBytes(defaultApexRow.toMap());
            } catch (JsonProcessingException e) {
                return null;
            }

        }
        return null;
    }

    @Override
    public ApexRow deserialize(byte[] bytes) {
        // TODO 祁灵 2025/6/23 17:02: 先这样吧
        if (apexRowClass.isEmpty() || apexRowClass.equals("DefaultApexRow")) {
            Map<String, Object> map;
            try {
                map = objectMapper.readValue(bytes, new TypeReference<Map<String, Object>>() {
                });
                ApexRow apexRow = new DefaultApexRow(map);
                return apexRow;
            } catch (IOException e) {
                log.error("deserialize error", e);
            }
        }
        return null;
    }
}
