package omni.audit.apex.api.factory;

import omni.audit.apex.api.transform.ApexTransform;

/**
 * <AUTHOR>
 * @interface ApexTransformFactory
 * @created 2025/6/17 11:29
 * @desc
 **/
public interface ApexTransformFactory extends Factory {

    default <T> ApexTransform<T> createTransform(ApexTransformFactoryContext context) {
        throw new UnsupportedOperationException(
                "The Factory has not been implemented and the deprecated Plugin will be used.");
    }
}
