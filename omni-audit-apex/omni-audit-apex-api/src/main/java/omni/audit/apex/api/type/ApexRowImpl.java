package omni.audit.apex.api.type;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @class ApexRowImpl
 * @created 2025/6/20 16:41
 * @desc Implementation of ApexRow
 **/
public class ApexRowImpl implements ApexRow {

    private Map<String, Object> data;

    public ApexRowImpl() {
        data = new LinkedHashMap<>();
    }

    public ApexRowImpl(Map<String, Object> data) {
        this.data = new LinkedHashMap<>(data);
    }

    @Override
    public <T> T get(String field, Class<T> clazz) {
        Object value = data.get(field);
        if (value == null) {
            return null;
        }
        return (T) value;
    }

    @Override
    public <T> void set(String field, T value) {
        data.put(field, value);
    }
}
