package omni.audit.apex.api.factory;

import omni.audit.apex.api.config.ReadonlyConfig;

/**
 * <AUTHOR>
 * @class FactoryContext
 * @created 2025/6/19 16:35
 * @desc
 **/
public class FactoryContext {

    private final ReadonlyConfig options;

    private final ClassLoader classLoader;

    public FactoryContext(ReadonlyConfig options, ClassLoader classLoader) {
        this.options = options;
        this.classLoader = classLoader;
    }
}
