package omni.audit.apex.core.faker;

import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.source.SourceReader;

import java.time.Instant;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @class FakerSourceReader
 * @created 2025/6/18 15:47
 * @desc
 **/
@Slf4j
public class FakerSourceReader implements SourceReader {
    @Override
    public void open() {

    }

    @Override
    public void close() {

    }

    @Override
    public Collection pollNext() {
        log.info("poll next");
        try {
            TimeUnit.SECONDS.sleep(2);
        } catch (InterruptedException e) {

        }
        return Collections.singleton(Instant.now().toEpochMilli());
    }
}
