package omni.audit.apex.core.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.core.config.TransformConfig;
import omni.audit.apex.core.entity.EventContext;

import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;

@Slf4j
public class HandlerProvider {
    TransformConfig transformConfig = new TransformConfig();
    ObjectMapper objectMapper = new ObjectMapper();

    private final Map<String, Handler> HANDLER_MAP = new HashMap<>();

    public void initMap(){
        ServiceLoader<HandlerFactory> load = ServiceLoader.load(HandlerFactory.class);
        for (HandlerFactory handlerFactory : load) {
            Handler handler = handlerFactory.createHandler();
            String name = handler.name();
            HANDLER_MAP.put(name,handler);
        }
    }

    public void createHandler(){
        JsonNode jsonArray = transformConfig.getJsonNode();
        if (!jsonArray.isArray()){
            throw new RuntimeException("flow.json is not array");
        }
        for (JsonNode jsonNode : jsonArray) {
            String name = jsonNode.get("name").asText();
            Map<String,Object> map = objectMapper.convertValue(jsonNode.get("config"), Map.class);
            Handler handler = HANDLER_MAP.get(name);
        }
    }

    public void process(EventContext eventContext) {
        JsonNode jsonArray = transformConfig.getJsonNode();
        for (JsonNode jsonNode : jsonArray) {
            String name = jsonNode.get("name").asText();
            Handler handler = HANDLER_MAP.get(name);
            handler.handle(eventContext);
        }
    }

    public void destroy(){
        JsonNode jsonArray = transformConfig.getJsonNode();
        for (JsonNode jsonNode : jsonArray) {
            String name = jsonNode.get("name").asText();
            Handler handler = HANDLER_MAP.get(name);
            handler.destroy();
        }
    }


}
