package omni.audit.apex.core.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

public class TransformConfig {

    private JsonNode jsonNode = null;

    public TransformConfig() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("flow.json");
            if (inputStream != null) {
                jsonNode = mapper.readTree(inputStream);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to load flow.json configuration", e);
        }
    }

    public JsonNode getJsonNode() {
        return jsonNode;
    }
}
