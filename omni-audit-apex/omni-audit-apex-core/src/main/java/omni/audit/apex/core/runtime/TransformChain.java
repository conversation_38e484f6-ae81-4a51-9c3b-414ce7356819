package omni.audit.apex.core.runtime;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.core.dag.TransformAction;

import java.util.List;

/**
 * <AUTHOR>
 * @class TransformJob
 * @created 2025/6/18 10:38
 * @desc
 **/
@Slf4j
public class TransformChain {

    @Setter
    private List<TransformAction> transforms;

    public ApexRecord transform(ApexRecord event) {
        Object value = event.getValue();
        for (TransformAction transform : transforms) {
            if (value == null) {
                // 过滤会将值置空
                break;
            }
            try {
                value = transform.getTransform().transform(value);
            } catch (Exception e) {
                log.error("transform error", e);

            }
        }
        if (value == null) {
            return null;
        }

        event.setValue(value);
        return event;

    }
}
