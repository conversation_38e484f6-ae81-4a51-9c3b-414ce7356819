package omni.audit.apex.core.runtime;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.core.dag.SourceAction;

import java.util.Collection;

/**
 * <AUTHOR>
 * @class JobTask
 * @created 2025/6/18 10:31
 * @desc
 **/
@Slf4j
@Data
public class JobTask implements Runnable {

    public boolean RUNNING;

    private SourceAction reader;

    private ApexQueue queue;

    public JobTask() {
    }

    public JobTask(SourceAction reader, ApexQueue queue) {
        this.reader = reader;
        this.queue = queue;
    }

    @Override
    public void run() {

        reader.open();
        queue.start();

        RUNNING = true;
        while (RUNNING) {
            try {
                Collection collection = reader.pollNext();
                collection.forEach(queue::publish);

            } catch (Exception e) {
                log.error("job task error", e);
            }
        }
    }

    public void stop() {
        RUNNING = false;

        reader.close();
        queue.stop();
    }
}
