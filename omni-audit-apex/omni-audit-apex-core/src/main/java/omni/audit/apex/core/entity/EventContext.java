package omni.audit.apex.core.entity;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class EventContext {
    private Map<String, Object> context = new HashMap<>();

    public EventContext(Map<String, Object> context) {
        this.context = context;
    }

    public Object getField(String path) {
        try {
            String[] split = path.split("\\.");
            Map<String, Object> temp = context;
            for (int i = 0; i < split.length; i++) {
                if (i == split.length - 1) {
                    return temp.get(split[i]);
                }else {
                    temp = (Map<String, Object>) temp.get(split[i]);
                }
            }
        }catch (Exception e){
            //log.error("get field error", e);
        }
        return null;
    }

    public void setField(String path, Object value) {
        try {
            String[] split = path.split("\\.");
            // 临时变量，用于遍历和修改
            Map<String, Object> temp = context;
            for (int i = 0; i < split.length; i++) {
                if (i == split.length - 1) {
                    temp.put(split[i], value);
                } else {
                    temp = (Map<String, Object>) temp.computeIfAbsent(split[i], k -> new HashMap<>());
                }
            }
        }catch (Exception e){
            //log.error("set field error", e);
        }
    }


}
