package omni.audit.apex.core.runtime;

import com.lmax.disruptor.WorkHandler;

/**
 * <AUTHOR>
 * @class ApexRecordHandler
 * @created 2025/6/18 14:41
 * @desc
 **/
public class ApexRecordHandler implements WorkHandler<ApexRecord> {

    private TransformChain transformChain;

    private SinkChain sinkChain;

    public ApexRecordHandler(TransformChain transformChain, SinkChain sinkChain) {
        this.transformChain = transformChain;
        this.sinkChain = sinkChain;
    }

    @Override
    public void onEvent(ApexRecord event) throws Exception {
        ApexRecord record = transformChain.transform(event);
        if (record == null || record.getValue() == null) {
            return;
        }
        sinkChain.sink(record);
    }
}
