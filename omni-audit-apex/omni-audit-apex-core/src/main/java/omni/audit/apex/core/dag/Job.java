package omni.audit.apex.core.dag;

import lombok.Getter;
import omni.audit.apex.api.config.EnvCommonOptions;
import omni.audit.apex.api.config.ReadonlyConfig;
import omni.audit.apex.api.config.SourceCommonOptions;
import omni.audit.apex.api.factory.FactoryUtil;
import omni.audit.apex.api.sink.ApexSink;
import omni.audit.apex.api.sink.SinkWriter;
import omni.audit.apex.api.sink.SinkWriterContext;
import omni.audit.apex.api.source.ApexSource;
import omni.audit.apex.api.source.SourceReader;
import omni.audit.apex.api.source.SourceReaderContext;
import omni.audit.apex.api.transform.ApexTransform;
import omni.audit.apex.core.runtime.DisruptorApexQueue;
import omni.audit.apex.core.runtime.JobTask;
import omni.audit.apex.core.runtime.SinkChain;
import omni.audit.apex.core.runtime.TransformChain;
import omni.audit.apex.shade.com.typesafe.config.Config;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @class Job
 * @created 2025/6/17 19:33
 * @desc
 **/
public class Job {

    Config envConfig;
    Config sourceConfig;
    List<? extends Config> transformConfigs;
    List<? extends Config> sinkConfigs;

    private List<JobTask> jobTasks;
    @Getter
    private String uid;
    private String jobName;

    public Job(Config envConfig,
               Config sourceConfig,
               List<? extends Config> transformConfigs,
               List<? extends Config> sinkConfigs) {
        this.envConfig = envConfig;
        this.sourceConfig = sourceConfig;
        this.transformConfigs = transformConfigs;
        this.sinkConfigs = sinkConfigs;
    }

    public void parser() {
        // 用来设置每个 source 下游任务的并行度，比如 source 设置为 2，env 设置为 4 ，那么会创建 2 个 source 用来消费，每个 source 创建 4 个来消费 source 拉取的数据
        // 用来设置 source 的并行度
        ClassLoader classLoader = this.getClass().getClassLoader();
        // 没有任务名就使用 默认前缀 + uid
        this.jobName = envConfig.getString(EnvCommonOptions.JOB_NAME.key());
        int sourceParallelism = sourceConfig.getInt(SourceCommonOptions.PARALLELISM.key());
        int parallelism = envConfig.getInt(EnvCommonOptions.PARALLELISM.key());
        ApexSource apexSource = FactoryUtil.createSource(ReadonlyConfig.fromConfig(sourceConfig), classLoader);
        TransformChain transformChain = buildTransform(envConfig, transformConfigs, classLoader);
        SinkChain sinkChain = buildSink(envConfig, sinkConfigs, classLoader);
        jobTasks = new ArrayList<>();
        // 创建任务并行实例
        for (int i = 0; i < sourceParallelism; i++) {
            JobTask jobTask = new JobTask();
            SourceReader reader = apexSource.createReader(new SourceReaderContext(i));
            SourceAction sourceAction = new SourceAction(reader);
            jobTask.setReader(sourceAction);
            DisruptorApexQueue apexQueue = new DisruptorApexQueue(ReadonlyConfig.fromConfig(envConfig), transformChain, parallelism, sinkChain);
            jobTask.setQueue(apexQueue);
            jobTasks.add(jobTask);
        }
    }

    public TransformChain buildTransform(Config envConfig, List<? extends Config> configs, ClassLoader classLoader) {
        List<TransformAction> actions = new LinkedList<>();
        for (Config config : configs) {
            ApexTransform apexTransform = FactoryUtil.createTransform(ReadonlyConfig.fromConfig(config), classLoader);
            TransformAction action = new TransformAction(apexTransform);
            actions.add(action);
        }
        TransformChain transformChain = new TransformChain();
        transformChain.setTransforms(actions);
        return transformChain;
    }

    public SinkChain buildSink(Config envConfig, List<? extends Config> configs, ClassLoader classLoader) {
        List<SinkAction> actions = new LinkedList<>();
        for (Config config : configs) {
            ReadonlyConfig readonlyConfig = ReadonlyConfig.fromConfig(config);
            ApexSink apexSink = FactoryUtil.createSink(readonlyConfig, classLoader);
            SinkWriter writer = apexSink.createWriter(new SinkWriterContext() {

            });
            SinkAction action = new SinkAction(writer);
            actions.add(action);
        }
        SinkChain sinkChain = new SinkChain();
        sinkChain.setSinks(actions);
        return sinkChain;
    }

    public void start() {
        CountDownLatch countDownLatch = new CountDownLatch(jobTasks.size());
        for (JobTask jobTask : jobTasks) {
            Thread thread = new Thread(() -> {
                try {
                    jobTask.run();
                } catch (Exception e) {
                    countDownLatch.countDown();
                }
            });
            thread.setDaemon(true);
            thread.start();
        }

        try {
            countDownLatch.await(); // 主线程阻塞直到所有任务完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 处理中断
        }
    }

    public void stop() {

    }
}
