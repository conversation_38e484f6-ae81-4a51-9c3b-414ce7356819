package omni.audit.common.util;

import omni.audit.common.util.enums.LevelEnum;

import java.util.List;
import java.util.Map;

public class HandleUtil {
    private static final String ALL_KEYS = "?";

    public static void updateMapValue(Map<String, Object> currentMap, String[] pathParts, int currentIndex, Object newValue, boolean cover) {
        if (currentMap ==null) {
            return;
        }
        if (currentIndex >= pathParts.length) {
            return;
        }

        String currentPath = pathParts[currentIndex];
        boolean isLast = currentIndex == pathParts.length - 1;

        if (currentPath.equals(ALL_KEYS)) {
            // 处理通配符，遍历所有键
            for (Map.Entry<String, Object> entry : currentMap.entrySet()) {
                if (isLast) {
                    // 如果是最后一个路径部分，直接设置值
                    if (cover){
                        currentMap.put(entry.getKey(), newValue);
                    }else {
                        currentMap.putIfAbsent(entry.getKey(), newValue);
                    }
                    currentMap.put(entry.getKey(), newValue);
                } else if (entry.getValue() instanceof Map) {
                    // 递归处理嵌套Map
                    updateMapValue((Map<String, Object>) entry.getValue(),
                            pathParts, currentIndex + 1, newValue,cover);
                }
            }
        } else if (currentMap.containsKey(currentPath)) {
            if (isLast) {
                // 如果是最后一个路径部分，直接设置值
                currentMap.put(currentPath, newValue);
            } else if (currentMap.get(currentPath) instanceof Map) {
                // 递归处理嵌套Map
                updateMapValue((Map<String, Object>) currentMap.get(currentPath),
                        pathParts, currentIndex + 1, newValue,cover);
            }
        }
    }

    public static void updateValueByPath(Map<String, Object> map, String path, String newValue) {
        String[] keys = path.split("\\.");
        updateValue(map, keys, 0, newValue);
    }

    private static void updateValue(Map<String, Object> currentMap, String[] keys, int index, String newValue) {
        if (currentMap == null) {
            return;
        }
        if (index >= keys.length) {
            return;
        }

        String currentKey = keys[index];

        if (currentKey.equals("?")) {
            // 通配符处理：遍历所有子 Map
            for (Object value : currentMap.values()) {
                if (value instanceof Map) {
                    updateValue((Map<String, Object>) value, keys, index + 1, newValue);
                }
            }
        } else {
            if (index == keys.length - 1) {
                // 最后一个键，直接赋值
                Object oldValue = currentMap.get(currentKey);
                if (oldValue != null){
                    if (! LevelEnum.oldGteNew((String) oldValue, newValue)){
                        currentMap.put(currentKey, newValue);
                    }
                }else {
                    currentMap.put(currentKey, newValue);
                }
            } else {
                // 继续深入下一层
                Object next = currentMap.get(currentKey);
                if (next instanceof Map) {
                    updateValue((Map<String, Object>) next, keys, index + 1, newValue);
                }
                // 如果不是 Map 类型，则忽略（或者可以根据需求抛出异常）
            }
        }
    }

}
