package omni.audit.common.util.enums;


public enum LevelEnum {
    l1("l1"),
    l2("l2"),
    l3("l3"),
    l4("l4"),
    l5("l5"),
    l6("l6"),
    l7("l7"),
    l8("l8"),
    l9("l9"),
    l10("l10");
    private final String level;
    LevelEnum(String level) {
        this.level = level;
    }

    public String getLevel() {
        return level;
    }

    public static LevelEnum getEnum(String level) {
        for (LevelEnum levelEnum : LevelEnum.values()) {
            if (levelEnum.getLevel().equals(level)) {
                return levelEnum;
            }
        }
        return null;
    }

    public static boolean oldGteNew(String oldLevel,String newLevel) {
        LevelEnum oldLevelEnum = null;
        LevelEnum newLevelEnum = null;
        for (LevelEnum levelEnum : LevelEnum.values()) {
            if (levelEnum.getLevel().equals(oldLevel)) {
                oldLevelEnum = levelEnum;
            }if (levelEnum.getLevel().equals(newLevel)) {
                newLevelEnum = levelEnum;
            }
        }
        if (oldLevelEnum == null || newLevelEnum == null) {
            return true;
        }
        if (oldLevelEnum.ordinal() >= newLevelEnum.ordinal()) {
            return true;
        }
        return false;
    }
}
