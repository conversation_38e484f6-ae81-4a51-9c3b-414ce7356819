package omni.audit.common.util;

import java.text.MessageFormat;

/**
 */
public class ServiceException extends RuntimeException {

    public ServiceException(Exception e) {
        super(e.getMessage());
    }

    public ServiceException(String msg) {
        super(msg);
    }

    public ServiceException(String msg, Exception e) {
        super(msg, e);
    }

    public ServiceException(String placeholderMsg, Object... keys) {
        super(MessageFormat.format(placeholderMsg, keys));
    }
}
