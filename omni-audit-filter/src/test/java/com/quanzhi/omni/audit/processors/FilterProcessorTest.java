package com.quanzhi.omni.audit.processors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quanzhi.omni.audit.filter.FilterConfigModel;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.RuleEngine;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@Slf4j
@RunWith(JUnit4.class)
@Ignore
public class FilterProcessorTest {

    private FilterProcessor processorSpy;

    @Before
    public void setup() {

        // 创建一个FilterProcessor的spy对象，这样我们可以模拟部分方法
        processorSpy = new FilterProcessor(
                "*********************************************",
                "postgres",
                "password123",
                "public");
        RuleEngine ruleEngine = new RuleEngine();
        List<FilterConfigModel> filterConfigModels = getFilterConfigModels();
        Map<String,CompiledRule> map = new HashMap<>();
        for (FilterConfigModel configModel : filterConfigModels) {
            if(configModel.getMatchRule() == null){
                continue;
            }
            MatchRule matchRule = configModel.getMatchRule();
            map.put(configModel.getId(), ruleEngine.compile(matchRule));
        }
        processorSpy.setCompiledRuleMap(map);
    }

    /**
     * 测试空事件的情况
     */
    @Test
    public void testExecuteWithEmptyEvent() {
        // 准备
        Map<String, Object> emptyEvent = new HashMap<>();

        // 执行
        boolean result = processorSpy.execute(emptyEvent);

        // 验证
        assertFalse("空事件应该过滤", result);
    }

    /**
     * 测试通过过滤的情况
     */
    @Test
    public void testExecuteWithPassingFilter() {
        // 准备
        Map<String, Object> testEvent = new HashMap<>();
        testEvent.put("key1", "value1");
        testEvent.put("key2", 123);

        // 执行
        boolean result = processorSpy.execute(testEvent);

        // 验证
        assertTrue("应该通过过滤", result);
    }

    /**
     * 测试未通过过滤的情况
     */
    @Test
    public void testExecuteWithFailingFilter() {
        // 准备
        Map<String, Object> testEvent = new HashMap<>();
        testEvent.put("key1", "value");
        testEvent.put("key2", 123);

        // 执行
        boolean result = processorSpy.execute(testEvent);

        // 验证
        assertFalse("不应该通过过滤", result);
    }

    /**
     * 测试passFilter方法 - SAVE类型通过的情况
     */
    @Test
    public void testPassFilterWithSaveTypePass() {

        Map<String, Object> testEvent = new HashMap<>();
        testEvent.put("key1", "value1");

        // 执行
        boolean result = processorSpy.passFilter(testEvent);

        // 验证
        assertTrue("SAVE类型匹配时应该通过", result);
    }

    /**
     * 测试passFilter方法 - SAVE类型不通过的情况
     */
    @Test
    public void testPassFilterWithSaveTypeFail() {

        Map<String, Object> testEvent = new HashMap<>();
        testEvent.put("key1", "value2");

        // 执行
        boolean result = processorSpy.passFilter(testEvent);

        // 验证
        assertFalse("SAVE类型不匹配时不应该通过", result);
    }

    /**
     * 测试passFilter方法 - FILTER类型通过的情况
     */
    @Test
    public void testPassFilterWithFilterTypePass() {

        Map<String, Object> testEvent = new HashMap<>();
        testEvent.put("key2", "value1");
        testEvent.put("key1", "value1");

        // 执行
        boolean result = processorSpy.passFilter(testEvent);

        // 验证
        assertTrue("FILTER类型不匹配时应该通过", result);
    }

    /**
     * 测试passFilter方法 - FILTER类型不通过的情况
     */
    @Test
    public void testPassFilterWithFilterTypeFail() {

        Map<String, Object> testEvent = new HashMap<>();
        testEvent.put("key1", "value1");
        testEvent.put("key2", "value2");

        // 执行
        boolean result = processorSpy.passFilter(testEvent);

        // 验证
        assertFalse("FILTER类型匹配时不应该通过", result);
    }

    /**
     * 辅助方法 - 创建FilterConfigModel
     */
    private List<FilterConfigModel> getFilterConfigModels() {
        List<FilterConfigModel> filterConfigModels = new ArrayList<>();
        List<String> list = readAsStringList("filter.config.json");
        for (String s : list) {
            FilterConfigModel model = JSON.parseObject(s, FilterConfigModel.class);
            filterConfigModels.add(model);
        }
        return filterConfigModels;
    }

    /**
     * 从functions.json文件读取内容并转换为字符串列表
     * 每一行作为一个字符串元素
     * @param filePath 文件路径，如果为null则从classpath中读取
     * @return 字符串列表
     */
    public static List<String> readAsStringList(String filePath) {
        List<String> objectStrings = new ArrayList<>();
        // 读取functions.json文件
        InputStream inputStream = FilterProcessorTest.class.getClassLoader().getResourceAsStream(filePath);
        if (inputStream == null) {
            throw new RuntimeException("Could not find filter.config.json file");
        }
        String content = new BufferedReader(
                new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                .lines().collect(Collectors.joining("\n"));
        // 解析JSON数组
        JSONArray jsonArray = JSON.parseArray(content);

        // 将每个JSON对象转换为字符串并添加到列表中
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            objectStrings.add(jsonObject.toJSONString());
        }
        return objectStrings;
    }
}
