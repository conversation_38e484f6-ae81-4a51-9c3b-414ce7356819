package com.quanzhi.omni.audit.jdbc;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import org.apache.commons.dbutils.BeanProcessor;
import org.postgresql.util.PGobject;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

public class JsonbBeanProcessor extends BeanProcessor {

    @Override
    protected Object processColumn(ResultSet rs, int index, Class<?> propType) throws SQLException {
        Object value = rs.getObject(index);

        if (value instanceof PGobject) {
            PGobject pgObject = (PGobject) value;
            if ("jsonb".equals(pgObject.getType())) {
                String json = pgObject.getValue();
                if (propType == MatchRule.class) {
                    // 将 JSON 转换为 MatchRule 对象
                    return JSONObject.parseObject(json, MatchRule.class);
                }
                if (Map.class.isAssignableFrom(propType)) {
                    // 将 JSON 转换为 Map<String, Object>
                    return JSONObject.parseObject(json, Map.class);
                }
            }
        }

        // 默认处理
        return super.processColumn(rs, index, propType);
    }
}
