package com.quanzhi.omni.audit.jdbc;

import com.alibaba.fastjson.JSONObject;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import org.apache.commons.dbutils.BeanProcessor;
import org.postgresql.util.PGobject;

import java.beans.PropertyDescriptor;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 结合了 JsonbBeanProcessor 和 CamelCaseBeanProcessor 的功能，
 * 既能处理 jsonb 类型转换，又能处理列名的转换（从下划线命名转为驼峰命名）
 */
public class CamelCaseJsonbBeanProcessor extends BeanProcessor {

    /**
     * 处理特殊类型的列，例如 jsonb 类型
     */
    @Override
    protected Object processColumn(ResultSet rs, int index, Class<?> propType) throws SQLException {
        Object value = rs.getObject(index);

        if (value instanceof PGobject) {
            PGobject pgObject = (PGobject) value;
            if ("jsonb".equals(pgObject.getType())) {
                String json = pgObject.getValue();
                if (propType == MatchRule.class) {
                    // 将 JSON 转换为 MatchRule 对象
                    return JSONObject.parseObject(json, MatchRule.class);
                }
                if (Map.class.isAssignableFrom(propType)) {
                    // 将 JSON 转换为 Map<String, Object>
                    return JSONObject.parseObject(json, Map.class);
                }
            }
        }

        // 处理数组类型
        if (propType.equals(java.util.List.class)) {
            java.sql.Array array = rs.getArray(index);
            if (array == null) {
                return new java.util.ArrayList<>(); // 返回空列表，避免 null
            }
            Object[] objects = (Object[]) array.getArray();
            return java.util.Arrays.asList(objects)
                    .stream()
                    .map(Object::toString)
                    .collect(java.util.stream.Collectors.toList());
        }

        // 默认处理
        return super.processColumn(rs, index, propType);
    }

    /**
     * 重写列名到属性的映射方法，将下划线命名转为驼峰命名
     */
    @Override
    protected int[] mapColumnsToProperties(ResultSetMetaData rsmd, PropertyDescriptor[] props) throws SQLException {
        int cols = rsmd.getColumnCount();
        int[] columnToProperty = new int[cols + 1];
        Arrays.fill(columnToProperty, PROPERTY_NOT_FOUND);

        // 创建属性名到属性索引的映射
        Map<String, Integer> propertyMap = new HashMap<>();
        for (int i = 0; i < props.length; i++) {
            propertyMap.put(props[i].getName().toLowerCase(), i);
        }

        // 遍历所有列，将列名转换为camelCase，然后查找匹配的属性
        for (int col = 1; col <= cols; col++) {
            String columnName = rsmd.getColumnLabel(col);
            if (columnName == null || columnName.isEmpty()) {
                columnName = rsmd.getColumnName(col);
            }
            
            // 将snake_case转换为camelCase
            String camelCaseColumnName = snakeToCamelCase(columnName);
            
            // 查找匹配的属性
            Integer propertyIndex = propertyMap.get(camelCaseColumnName.toLowerCase());
            if (propertyIndex != null) {
                columnToProperty[col] = propertyIndex;
            }
        }

        return columnToProperty;
    }

    /**
     * 将下划线命名转为驼峰命名
     */
    private String snakeToCamelCase(String snakeCase) {
        if (snakeCase == null || snakeCase.isEmpty()) {
            return snakeCase;
        }

        // 分割字符串
        String[] parts = snakeCase.split("_");
        StringBuilder camelCase = new StringBuilder(parts[0].toLowerCase());

        // 将每个部分的首字母大写
        for (int i = 1; i < parts.length; i++) {
            if (parts[i].length() > 0) {
                camelCase.append(Character.toUpperCase(parts[i].charAt(0)));
                if (parts[i].length() > 1) {
                    camelCase.append(parts[i].substring(1).toLowerCase());
                }
            }
        }

        return camelCase.toString();
    }
}
