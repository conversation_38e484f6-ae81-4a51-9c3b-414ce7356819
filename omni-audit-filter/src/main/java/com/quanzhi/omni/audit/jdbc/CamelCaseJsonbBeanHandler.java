package com.quanzhi.omni.audit.jdbc;

import org.apache.commons.dbutils.handlers.BeanHandler;

/**
 * 结合了 jsonb 类型转换和列名转换（从下划线命名转为驼峰命名）的 BeanHandler
 */
public class CamelCaseJsonbBeanHandler<T> extends BeanHandler<T> {

    /**
     * 创建一个新的 CamelCaseJsonbBeanHandler 实例
     *
     * @param type 返回对象的类型
     */
    public CamelCaseJsonbBeanHandler(Class<T> type) {
        super(type, new CamelCaseJsonbRowProcessor());
    }
}
