package com.quanzhi.omniaudit.sampling.service.strategy;

import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * create at 2025/4/25 14:18
 * @description: 基于内存的采样
 **/
public class MemorySamplingStrategy extends AbstractSamplingStrategy implements ISamplingService {

    private int basisCount = 16;
    private int maxCount = 32;

    public MemorySamplingStrategy(ICacheManager cacheManager, SamplingConfig samplingConfig) {
        super(cacheManager, samplingConfig);
        this.basisCount = samplingConfig.getBasisCount() != 0 ? Math.max(1, samplingConfig.getBasisCount()) : basisCount;
        this.maxCount = samplingConfig.getMaxCount() != 0 ? samplingConfig.getMaxCount() : maxCount;
    }

    @Override
    public boolean sampling(String key) {
        AtomicInteger at = sampleCache.get(key, k -> new AtomicInteger(0));
        if (at == null) {
            return true;
        }
        int currentCount = at.get();
        if (currentCount < basisCount) {
            at.incrementAndGet();
            return true;
        } else if (currentCount >= maxCount) {
            return probabilisticSamplingStrategy.sample();
        } else {
            // 在 basisCount 和 maxCount 之间，按一定概率采样
            float probability = (float) (maxCount - currentCount) / (maxCount - basisCount);
            if (Math.random() < probability) {
                at.incrementAndGet();
                return true;
            } else {
                return false;
            }
        }
    }
}