package com.quanzhi.omniaudit.sampling.moudle;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/4/28 11:24
 * @description:
 **/
@Data
public class MultiDimensionalSamplingConfig extends SamplingConfig {
    /**
     * 最小采样数
     */
    private int SAMPLE_MIN_COUNT = 4;
    /**
     * 总的样例数
     */
    private int SAMPLE_COUNT = 100;
    /**
     * 单维度最多
     */
    private int SIMPLE_KIND_COUNT = 20;
    /**
     * 单种样例最多
     */
    private int SINGLE_SIMPLE_KIND_COUNT = 2;
    /**
     * 采样维度
     */
    private List<String> dimensionTypes;


}