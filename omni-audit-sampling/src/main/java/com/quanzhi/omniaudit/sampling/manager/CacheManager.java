package com.quanzhi.omniaudit.sampling.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * create at 2025/5/19 20:46
 * @description: 缓存管理器
 **/
@Slf4j
public class CacheManager implements ICacheManager {

    private final Map<String, Cache> cacheMap;
    private final Long defaultCacheMaximumSize;
    private final Long defaultCacheExpireAfterAccess;

    public CacheManager() {
        this(100000L, 3*60*60*1000L);
    }

    public CacheManager(Long defaultCacheMaximumSize, Long defaultCacheExpireAfterAccess) {
        this.cacheMap = new ConcurrentHashMap<>();
        this.defaultCacheMaximumSize = defaultCacheMaximumSize;
        this.defaultCacheExpireAfterAccess = defaultCacheExpireAfterAccess;
    }

    @Override
    public <T, V> Cache<T, V> getOrCreateCache(String cacheName) {
        return getOrCreateCache(cacheName, 0, 0);
    }

    @Override
    public <T, V> Cache<T, V> getOrCreateCache(String cacheName, long expireMils, long size) {
        return getOrCreateCache(cacheName, expireMils, size, null);
    }

    @Override
    public <T, V> Cache<T, V> getOrCreateCache(String cacheName, long expireMils, long size, CacheRemovalListener<T, V> listener) {
        return getOrCreateCache(cacheName, expireMils, size, null, listener);
    }

    @Override
    public <T, V> Cache<T, V> getOrCreateCache(String cacheName, long expireMils, long maximumSize, ExpireType expireType, CacheRemovalListener<T, V> listener) {
        Objects.requireNonNull(cacheName, "Cache name must not be null");
        return (Cache<T, V>) cacheMap.computeIfAbsent(cacheName, k -> createCache(cacheName, expireMils, maximumSize, expireType, listener));
    }

    @Override
    public <T, V> Cache<T, V> createCache(String cacheName, long expireMils, long size, ExpireType expireType, CacheRemovalListener<T, V> listener) {
        Caffeine<Object, Object> caffeine = Caffeine.newBuilder().recordStats();
        if (listener != null) {
            caffeine.removalListener(listener);
        }
        caffeine.maximumSize(size > 0 ? size : defaultCacheMaximumSize);
        if (expireType == ExpireType.AFTER_ACCESS) {
            caffeine.expireAfterAccess(expireMils > 0 ? expireMils : defaultCacheExpireAfterAccess, TimeUnit.MILLISECONDS);
        } else if (expireType == ExpireType.AFTER_WRITE) {
            caffeine.expireAfterWrite(expireMils > 0 ? expireMils : defaultCacheExpireAfterAccess, TimeUnit.MILLISECONDS);
        } else {
            caffeine.expireAfterAccess(expireMils > 0 ? expireMils : defaultCacheExpireAfterAccess, TimeUnit.MILLISECONDS);
        }
        Cache<T, V> cache = caffeine.build();
        log.info("create cache, cacheName: {}, maximumSize: {}, expireAfterAccess: {}ms", cacheName, size, expireMils);
        return cache;
    }

    @Override
    public void removeCache(String cacheName) {
        Cache<?, ?> cache = cacheMap.remove(cacheName);
        if (cache != null) {
            cache.invalidateAll();
            log.info("removed cache: {}", cacheName);
        }
    }

    @Override
    public void clearAllCaches() {
        cacheMap.values().forEach(Cache::invalidateAll);
        cacheMap.clear();
        log.info("cleared all caches");
    }

    public interface CacheRemovalListener<T, V> extends RemovalListener<T, V> {

    }
}