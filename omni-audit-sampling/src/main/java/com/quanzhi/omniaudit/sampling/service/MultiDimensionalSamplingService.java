package com.quanzhi.omniaudit.sampling.service;


import com.quanzhi.omniaudit.sampling.moudle.MultiDimensionalSamplingConfig;
import com.quanzhi.omniaudit.sampling.moudle.SampleObj;
import com.quanzhi.omniaudit.sampling.moudle.SamplingKeys;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * create at 2025/4/28 11:13
 * @description: 多维度采样
 * <p>
 * 1、一个对象可以按各自维度采样，采样配置通过参数控制
 * 2、考虑不同维度的采样，每个key的转换逻辑由客户端调用。
 **/
@Slf4j
public class MultiDimensionalSamplingService {

    private final MultiDimensionalSamplingConfig samplingConfig;

    public MultiDimensionalSamplingService(MultiDimensionalSamplingConfig samplingConfig) {
        this.samplingConfig = samplingConfig;
    }

    public <T> T process(Map<String, Object> sourceObj) {
        List<T> samples = (List<T>) sourceObj.get("samples");
        T sample = createSample(sourceObj);
        if (checkSample(samples, sample)) {
            return sample;
        }
        return null;
    }


    private <T> T createSample(Map<String, Object> sourceObj) {
        //tod 根据元数据
        return (T) sourceObj;
    }


    /**
     * @param samples 已采到的样例
     * @param sample  当前样例
     * @param <T>
     * @return
     */
    public <T> boolean checkSample(List<T> samples, T sample) {
        if (samples.size() < samplingConfig.getSAMPLE_MIN_COUNT()) {
            return true;
        }
        if (samples.size() >= samplingConfig.getSAMPLE_COUNT()) {
            return false;
        }
        if (sample == null) {
            return false;
        }
        List<String> dimensionTypes = samplingConfig.getDimensionTypes();
        if (dimensionTypes == null || dimensionTypes.isEmpty()) {
            return false;
        }
        //转换
        SamplingKeys samplingKeys = new SamplingKeys();
        for (T t : samples) {
            for (String dimensionType : dimensionTypes) {
                Object dimValue = getDimValue(t, dimensionType);
                samplingKeys.add(dimensionType, dimValue);
            }
        }
        //判断要不要采
        for (String dimensionType : dimensionTypes) {
            Object dimValue = getDimValue(sample, dimensionType);
            if (samplingKeys.getDimensionMap().get(dimensionType)!=null&&
                    samplingKeys.getDimensionMap().get(dimensionType).size() < samplingConfig.getSIMPLE_KIND_COUNT()
                    && checkHasKeyNotFull(samplingKeys, dimensionType, dimValue)
            ) {
                return true;
            }
        }
        return false;
    }

    private boolean checkHasKeyNotFull(SamplingKeys samplingKeys, String dimensionType, Object dimValue) {
        if (dimValue instanceof String) {
            return samplingKeys.get(dimensionType, (String) dimValue) < samplingConfig.getSINGLE_SIMPLE_KIND_COUNT();
        }
        if (dimValue instanceof Collection) {
            List<String> lists = (List<String>) dimValue;
            for (String value : lists) {
                if (samplingKeys.get(dimensionType, value) < samplingConfig.getSINGLE_SIMPLE_KIND_COUNT()) {
                    return true;
                }
            }
        }
        return false;
    }

    private <T> Object getDimValue(T sampleObj, String dimensionType) {
        Object fieldValue;
        if (sampleObj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) sampleObj;
            if (map.containsKey(dimensionType)) {
                return map.get(dimensionType);
            }
        }
        fieldValue = getFieldValue(sampleObj, dimensionType);
        if (fieldValue != null) {
            return fieldValue;
        }
        if (sampleObj instanceof SampleObj) {
            SampleObj obj = (SampleObj) sampleObj;
            if (obj.getFieldConverter() != null && obj.getFieldConverter().getConverter(dimensionType) != null) {
                String apply = (String) obj.getFieldConverter().getConverter(dimensionType).apply(sampleObj);
                return apply;
            }
        }
        return null;
    }

    private static <T> Object getFieldValue(T obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return value;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return null;
        }
    }
}