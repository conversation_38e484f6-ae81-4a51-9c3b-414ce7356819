package com.quanzhi.omniaudit.sampling.service.strategy;

import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;

import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * create at 2025/4/27 14:57
 * @description: 随机采样
 **/
public class ProbabilisticSamplingStrategy implements ISamplingService {

    private float prob;

    private long positiveSamplingBoundary;

    private long negativeSamplingBoundary;

    public ProbabilisticSamplingStrategy() {

    }

    public ProbabilisticSamplingStrategy(float prob) {
        this.prob = prob;
        this.positiveSamplingBoundary = (long) (((1L << 63) - 1) * prob);
        this.negativeSamplingBoundary = (long) ((1L << 63) * prob);
    }

    public boolean sample() {
        if (prob >= 1) {
            return true;
        } else if (prob <= 0) {
            return false;
        }
        long next = ThreadLocalRandom.current().nextLong();
        if (next > 0) {
            return next <= this.positiveSamplingBoundary;
        } else {
            return next >= this.negativeSamplingBoundary;
        }
    }


    @Override
    public void init(SamplingConfig samplingConfig) {
        this.prob = samplingConfig.getProb();
        this.positiveSamplingBoundary = (long) (((1L << 63) - 1) * prob);
        this.negativeSamplingBoundary = (long) ((1L << 63) * prob);
    }

    @Override
    public boolean sampling(String key) {
        return sample();
    }

    @Override
    public void reset() {

    }
}