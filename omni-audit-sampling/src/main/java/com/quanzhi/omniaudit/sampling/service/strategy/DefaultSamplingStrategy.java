package com.quanzhi.omniaudit.sampling.service.strategy;

import com.github.benmanes.caffeine.cache.Cache;
import com.quanzhi.omniaudit.sampling.client.RedisClient;
import com.quanzhi.omniaudit.sampling.manager.ICacheManager;
import com.quanzhi.omniaudit.sampling.moudle.SamplingConfig;
import com.quanzhi.omniaudit.sampling.service.ISamplingService;
import omni.audit.common.client.redis.RedisClientWrapper;
import omni.audit.common.client.redis.RedisConfig;
import omni.audit.common.client.redis.RedissonClientFactory;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * create at 2025/5/19 20:16
 * @description: 默认采样策略
 **/
public class DefaultSamplingStrategy extends AbstractSamplingStrategy implements ISamplingService {

    private SamplingConfig samplingConfig;
    public static final int STEP_COUNT = (2 << 2) - 1;
    protected Duration expire = Duration.ofHours(24);
    private final RedisClientWrapper redisClientWrapper;

    public DefaultSamplingStrategy(ICacheManager cacheManager, SamplingConfig samplingConfig) {
        super(cacheManager, samplingConfig);
        RedisConfig redisConfig=new RedisConfig();
        redisConfig.setHost(samplingConfig.getRedisProperties().getHost());
        redisConfig.setPort(samplingConfig.getRedisProperties().getPort());
        redisConfig.setPassword(samplingConfig.getRedisProperties().getPassword());
        redisConfig.setTimeout(samplingConfig.getRedisProperties().getTimeout());
        RedissonClientFactory.init(redisConfig);
        this.redisClientWrapper=new RedisClientWrapper();

    }

    @Override
    public boolean sampling(String key) {
        // 从 redis 拿数据
        AtomicInteger at = sampleCache.get(key, s -> {
            try {
                int c = getAndIncrementByRedis(s, 0);
                if (c >= samplingConfig.getMaxCount()){
                    c = samplingConfig.getMaxCount();
                } else if (c > 0) {
                    // 这里会将计数器的余数进行清零，避免少采样（会造成多采样），例如 redis 记录 20，那么这里会将计数器重置 16
                    // 因为计数器是 STEP_COUNT 的步长进行更新的。
                    c = c - (c & STEP_COUNT);
                }
                return new AtomicInteger(c);
            } catch (Exception e) {
                // 如果redis宕机，那么当成第一次
                return new AtomicInteger(0);
            }
        });
        if (at == null) {
            return true;
        }
        if (at.get() < samplingConfig.getBasisCount()) {
            at.incrementAndGet();
            // 如果达到了步长，进行更新
            increment(key, at);
            return true;
        } else if (at.get()  < samplingConfig.getMaxCount()) {
            if (probSample()) {
                at.incrementAndGet();
                increment(key, at);
                return true;
            }
        }
        return false;
    }

    private void increment(String key, AtomicInteger at) {
        if (isRemain(at.get())) {
            int s;
            try {
                s = getAndIncrementByRedis(key, STEP_COUNT + 1);
                if (s == samplingConfig.getMaxCount() || s == samplingConfig.getBasisCount()) {
                    at.set(s);
                }
            }catch (Exception ignored){
            }
        }
    }

    private boolean isRemain(int count) {
        return (count & STEP_COUNT) == 0;
    }

    private int getAndIncrementByRedis(String key, int delta) {
        long count = redisClientWrapper.getRedissonClient().getAtomicLong(key).addAndGet(delta);
        if(count==0){
            //设置采样超时时间
            redisClientWrapper.getRedissonClient().getAtomicLong(key).expire(expire);
        }
        return (int) count;
    }

    @Override
    public void reset() {

    }
}