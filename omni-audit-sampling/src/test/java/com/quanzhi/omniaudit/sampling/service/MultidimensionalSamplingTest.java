package com.quanzhi.omniaudit.sampling.service;





import com.quanzhi.omniaudit.sampling.moudle.FieldConverter;
import com.quanzhi.omniaudit.sampling.moudle.MultiDimensionalSamplingConfig;
import com.quanzhi.omniaudit.sampling.moudle.Sample;
import com.quanzhi.omniaudit.sampling.moudle.SampleObj;
import junit.framework.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/4/28 14:50
 * @description:
 **/
public class MultidimensionalSamplingTest {


  private MultiDimensionalSamplingService samplingService;
  private MultiDimensionalSamplingConfig multidimensionalSamplingConfig;

    @BeforeEach
    public void init(){
        multidimensionalSamplingConfig=new MultiDimensionalSamplingConfig();
        multidimensionalSamplingConfig.setSAMPLE_COUNT(100);
        multidimensionalSamplingConfig.setSIMPLE_KIND_COUNT(20);
        multidimensionalSamplingConfig.setSINGLE_SIMPLE_KIND_COUNT(2);
        multidimensionalSamplingConfig.setDimensionTypes(Arrays.asList("method","reqType"));
        samplingService=new MultiDimensionalSamplingService(multidimensionalSamplingConfig);
    }


    /**
     * 测试同一属性key达到最大采样数，不采样
     */
    @Test
    public void testCheckSample1(){
        List<Sample> sampleList=new ArrayList<>();
        sampleList.add(Sample.builder().method("GET").reqType("xml").build());
        sampleList.add(Sample.builder().method("GET").reqType("xml").build());
        Sample sample = Sample.builder().method("GET").reqType("xml").build();
        boolean isSample = samplingService.checkSample(sampleList,sample);
        Assert.assertTrue(!isSample);
    }

    /**
     * 测试同一属性key没达到最大采样数，不采样
     */
    @Test
    public void testCheckSample2(){
        List<Sample> sampleList=new ArrayList<>();
        sampleList.add(Sample.builder().method("GET").reqType("xml").build());
        sampleList.add(Sample.builder().method("POST").reqType("xml").build());

        boolean isSample = samplingService.checkSample(sampleList,Sample.builder().method("GET").reqType("xml").build());
        Assert.assertTrue(isSample);

        isSample = samplingService.checkSample(sampleList,Sample.builder().method("POST").reqType("xml").build());
        Assert.assertTrue(isSample);
    }

    /**
     * 测试采样字段为list
     */
    @Test
    public void testCheckSample3(){
        List<Sample> sampleList=new ArrayList<>();
        sampleList.add(Sample.builder().dataLabels(Arrays.asList("phone","name")).build());
        sampleList.add(Sample.builder().dataLabels(Arrays.asList("phone","idcard")).build());
        multidimensionalSamplingConfig.setDimensionTypes(Arrays.asList("dataLabels"));
        boolean isSample = samplingService.checkSample(sampleList,Sample.builder().dataLabels(Arrays.asList("phone")).build());
        Assert.assertTrue(!isSample);
        isSample = samplingService.checkSample(sampleList,Sample.builder().dataLabels(Arrays.asList("name")).build());
        Assert.assertTrue(isSample);
    }

    /**
     * 测试自定义采样维度
     */
    @Test
    public void testCheckSample4(){
         FieldConverter<Sample> sampleFieldConverter = new FieldConverter<>();
         sampleFieldConverter.configureConverter("labelKey",(Object o)->{
             Sample s= (Sample) o;
             return s.getDataLabels().stream().collect(Collectors.joining("-"));
         });

        List<SampleObj> sampleList=new ArrayList<>();
        sampleList.add(Sample.builder().dataLabels(Arrays.asList("phone","name")).fieldConverter(sampleFieldConverter).build());
        sampleList.add(Sample.builder().dataLabels(Arrays.asList("phone","name")).fieldConverter(sampleFieldConverter).build());

        multidimensionalSamplingConfig.setDimensionTypes(Arrays.asList("labelKey"));
        SampleObj sample  = Sample.builder().method("GET").reqType("xml").dataLabels(Arrays.asList("phone","name")).fieldConverter(sampleFieldConverter).build();

        boolean isSample = samplingService.checkSample(sampleList,sample);
        Assert.assertTrue(!isSample);

    }








}