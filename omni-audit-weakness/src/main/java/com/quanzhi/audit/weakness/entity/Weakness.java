package com.quanzhi.audit.weakness.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * create at 2025/5/12 11:27
 * @description:
 **/
@Data
public class Weakness {

    /**
     * 数据库自增ID
     */
    private Integer id;
    /**
     * 资产唯一ID
     */
    private String assetId;
    /**
     * 弱点ID
     */
    private String weaknessId;
    /**
     * 操作ID
     */
    private String operationId;


    private String name;
    private String type;
    private String level;
    private String state;

    private String dbName;
    private String dbType;
    private String dbVersion;
    private String svcName;
    private String svcAddress;

    private List<String> visitDomains;
    private List<String> deployDomains;

    private Long createTime;
    private Long updateTime;
    private Long discoverTime;
    private Long activeTime;
}