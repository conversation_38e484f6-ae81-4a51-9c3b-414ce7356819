package com.quanzhi.audit.weakness.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.quanzhi.audit.weakness.constant.WeaknessConstant;
import com.quanzhi.audit.weakness.entity.WeaknessMetadata;
import com.quanzhi.audit.weakness.enums.State;
import com.quanzhi.audit.weakness.entity.Weakness;
import com.quanzhi.audit.weakness.entity.WeaknessRule;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.Decision;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.ValueType;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RuleCondition;
import lombok.extern.slf4j.Slf4j;
import omni.audit.common.util.DateUtil;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.Array;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * create at 2025/5/8 17:10
 * @description:
 **/
@Slf4j
public class WeaknessService {

    private RuleEngine ruleEngine = new RuleEngine();
    private List<WeaknessRule> weaknessRules;
    private final QueryRunner queryRunner;
    private final Properties weaknessConfig;


    public WeaknessService(JdbcConfig jdbcConfig, Properties weaknessConfig) {
        this.queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        this.weaknessConfig = weaknessConfig;
        init();
    }

    private void init() {
        this.weaknessRules = getWeaknessRules();
        for (WeaknessRule weaknessRule : weaknessRules) {
            weaknessRule.setCompiledRule(ruleEngine.compile(weaknessRule.getMatchRule()));
        }
    }

    public List<Map<String, Object>> process(Object rowData) {
        //弱点识别
        List<WeaknessMetadata> weaknessList = recognitionWeakness(rowData);
        //弱点处理
        List<Map<String, Object>> result = processWeakness(weaknessList, rowData);
        //聚合更新
        aggregateUpdates(result);
        return result;
    }

    private void aggregateUpdates(List<Map<String, Object>> result) {
        String primaryKeys = weaknessConfig.getProperty(WeaknessConstant.PRIMARY_KEYS, null);
        if (StringUtils.isEmpty(primaryKeys)) {
            return;
        }
        List<String> primaryKeyList = Stream.of(primaryKeys.split(",")).map(String::trim).collect(Collectors.toList());
        if (primaryKeyList.isEmpty()) {
            return;
        }
        //数据库字段映射 key内存字段  value 数据库字段
        Map<String, String> fieldMap = (Map<String, String>) weaknessConfig.get(WeaknessConstant.FIELD_MAP);

        String weaknessTable = weaknessConfig.getProperty(WeaknessConstant.WEAKNESS_TABLE, "weakness");
        String sql = "select * from " + weaknessTable + " where 1=1 ";

        for (Map<String, Object> map : result) {
            for (String primaryKey : primaryKeyList) {
                sql += " and " + fieldMap.get(primaryKey) + "=" + map.get(primaryKey).toString();
            }
            Map<String, Object> queryDbOne = null;
            try {
                queryDbOne = queryRunner.query(sql, new MapHandler());
            } catch (SQLException e) {
                log.error("query sql:{} error", sql);
            }
            if (queryDbOne == null) {
                continue;
            }
            Map<String, Object> transformMap = transformKey(queryDbOne, fieldMap);
            map.put("operationId",transformMap.get("operationId"));
            mergeFields(map, transformMap);
        }
    }

    private String createOperationId() {
        StringBuffer buffer = new StringBuffer();
        String formattedStr = RandomUtil.randomString(5);
        buffer.append("W").append("-").append(DateUtil.getDateTime(DateUtil.DATE_PATTERN.YYYYMMDD)).append("-").append(formattedStr);
        return buffer.toString();
    }

    /**
     * 按数据库字段映射
     */
    private Map<String, Object> transformKey(Map<String, Object> queryDbOne, Map<String, String> fieldMap) {
        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : queryDbOne.entrySet()) {
            String key = entry.getKey();
            String newKey = getNewKey(fieldMap, key);
            if (newKey != null) {
                result.put(newKey, entry.getValue());
            } else {
                result.put(key, entry.getValue());
            }
        }
        return result;
    }

    private String getNewKey(Map<String, String> fieldMap, String key) {
        AtomicReference<String> resultKey = new AtomicReference<>();
        fieldMap.forEach((dtoKey, dbKey) -> {
            if (dbKey.equals(key)) {
                resultKey.set(dtoKey);
            }
        });
        return resultKey == null ? null : resultKey.get();
    }

    private void mergeFields(Map<String, Object> targetMap, Map<String, Object> sourceMap) {
        for (Map.Entry<String, Object> entry : sourceMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Collection) {
                Collection<?> sourceCollection = (Collection<?>) value;
                Object targetValue = targetMap.get(key);

                if (targetValue == null) {
                    // 如果 targetMap 中没有该键，则直接放入一个新的不可变集合
                    targetMap.put(key, new HashSet<>(sourceCollection));
                } else if (targetValue instanceof Collection) {
                    // 如果 targetMap 中的值也是集合，则合并并去重
                    Collection<Object> mergedCollection = new HashSet<>();
                    mergedCollection.addAll((Collection<?>) targetValue);
                    mergedCollection.addAll(sourceCollection);
                    targetMap.put(key, mergedCollection);
                }
            }
        }
    }


    private List<Map<String, Object>> processWeakness(List<WeaknessMetadata> weaknessList, Object rowData) {
        Map<String, String> outputFieldMap = (Map<String, String>) weaknessConfig.get(WeaknessConstant.OUTPUT_FIELD_MAP);
        Map<String, Object> outputMap = new HashMap<>();
        outputFieldMap.forEach((targetField, sourceField) -> {
            outputMap.put(targetField, getFieldValue(rowData, sourceField));
        });
        Object timestamp = getFieldValue(rowData, "timestamp");
        List<Map<String, Object>> result = new ArrayList<>();
        for (WeaknessMetadata weaknessMetadata : weaknessList) {
            Map<String, Object> map = new HashMap<>();
            map.putAll(outputMap);
            map.put("weaknessId", weaknessMetadata.getWeaknessId());
            map.put("name", weaknessMetadata.getName());
            map.put("type", weaknessMetadata.getType());
            map.put("level", weaknessMetadata.getLevel());
            map.put("state", weaknessMetadata.getState());
            map.put("discoverTime", timestamp);
            map.put("activeTime", timestamp);
            map.put("operationId", createOperationId());
            result.add(map);
        }
        return result;
    }

    private void storageWeakness(List<Weakness> weaknessList) {
        for (Weakness weakness : weaknessList) {
            if (weakness.getId() != null) {
                //更新
                try {
                    DataSource dataSource = queryRunner.getDataSource();
                    Connection connection = dataSource.getConnection();
                    // 更新时间戳
                    weakness.setUpdateTime(System.currentTimeMillis());
                    // 创建数组参数
                    Array visitDomainsArray = connection.createArrayOf("text", weakness.getVisitDomains().toArray());
                    Array deployDomainsArray = connection.createArrayOf("text", weakness.getDeployDomains().toArray());

                    Object[] params = {
                            weakness.getName(),
                            weakness.getType(),
                            weakness.getLevel(),
                            weakness.getState(),
                            weakness.getDbName(),
                            weakness.getDbType(),
                            weakness.getDbVersion(),
                            weakness.getSvcName(),
                            weakness.getSvcAddress(),
                            visitDomainsArray,
                            deployDomainsArray,
                            weakness.getUpdateTime(),
                            weakness.getActiveTime(),
                            weakness.getId()
                    };

                    String updateSql = "UPDATE weakness SET " +
                            "name = ?, type = ?, level = ?, state = ?, dbName = ?, dbType = ?, dbVersion = ?, " +
                            "svcName = ?, svcAddress = ?, visitDomains = ?, deployDomains = ?, " +
                            "updateTime = ?, activeTime = ? " +
                            "WHERE id = ?";

                    int rowsAffected = queryRunner.update(updateSql, params);
                    if (rowsAffected > 0) {
                        log.info("Updated weakness with id: {}", weakness.getId());
                    } else {
                        log.warn("No rows affected when updating weakness with id: {}", weakness.getId());
                    }

                } catch (Exception e) {
                    log.error("update weakness error", e);
                }
            } else {
                try {
                    DataSource dataSource = queryRunner.getDataSource();
                    Connection connection = dataSource.getConnection();
                    weakness.setCreateTime(System.currentTimeMillis());
                    weakness.setUpdateTime(System.currentTimeMillis());
                    Object[] params = {
                            weakness.getAssetId(), weakness.getWeaknessId(),
                            weakness.getName(), weakness.getType(), weakness.getLevel(), weakness.getState(),
                            weakness.getDbName(), weakness.getDbType(), weakness.getDbVersion(),
                            weakness.getSvcName(), weakness.getSvcAddress(),
                            connection.createArrayOf("text", weakness.getVisitDomains().toArray()),
                            connection.createArrayOf("text", weakness.getDeployDomains().toArray()),
                            weakness.getCreateTime(), weakness.getUpdateTime(),
                            weakness.getDiscoverTime(), weakness.getActiveTime()
                    };
                    ScalarHandler<Long> rsh = new ScalarHandler<>();
                    queryRunner.insert("insert into weakness(assetId,weaknessId,name,type,level,state,dbName,dbType,dbVersion,svcName,svcAddress,visitDomains,deployDomains,createTime,updateTime,discoverTime,activeTime)" +
                            " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)", rsh, params);
                    log.info("insert a weakness", params);
                } catch (Exception e) {
                    log.error("insert weakness error", e);
                }
            }
        }
    }

    private Object getFieldValue(Object rowData, String fieldName) {
        if (rowData instanceof Map) {
            return ((Map<String, Object>) rowData).get(fieldName);
        } else {
            try {
                Field field = rowData.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(rowData);
                return value;
            } catch (NoSuchFieldException | IllegalAccessException e) {
                return null;
            }
        }
    }

//    private List<Weakness> processWeakness(List<WeaknessMetadata> weaknessList, Object rowData) {
//
//        String assetId = getFieldValue(rowData, "assetId").toString();
//        List<String> visitDomains = (List<String>) getFieldValue(rowData, "visitDomains");
//        List<String> deployDomains = (List<String>) getFieldValue(rowData, "deployDomains");
//        String dbName = getFieldValue(rowData, "dbName").toString();
//        String dbType = getFieldValue(rowData, "dbType").toString();
//        String dbVersion = getFieldValue(rowData, "dbVersion").toString();
//        String svcName = getFieldValue(rowData, "svcName").toString();
//        String svcAddress = getFieldValue(rowData, "svcAddress").toString();
//        Long eventTimeStamp = (Long) getFieldValue(rowData, "timestamp");
//
//
//        for (WeaknessMetadata weakness : weaknessList) {
//            String weaknessId = weakness.getWeaknessId();
//            try {
//                RowProcessor customProcessor = new BasicRowProcessor(new CustomBeanProcessor());
//                BeanHandler<Weakness> handler = new BeanHandler<>(Weakness.class, customProcessor);
//
//                Weakness dbWeakness = queryRunner.query("select * from weakness where assetId=? and weaknessId=?", handler, assetId, weaknessId);
//                weakness.setDbName(dbName);
//                weakness.setDbVersion(dbVersion);
//                weakness.setDbType(dbType);
//                weakness.setSvcName(svcName);
//                weakness.setSvcAddress(svcAddress);
//                weakness.setWeaknessId(weaknessId);
//                weakness.setAssetId(assetId);
//                if (dbWeakness == null) {
//                    weakness.setVisitDomains(visitDomains);
//                    weakness.setDeployDomains(deployDomains);
//                    weakness.setDiscoverTime(eventTimeStamp);
//                    weakness.setActiveTime(eventTimeStamp);
//                } else {
//                    weakness.setId(dbWeakness.getId());
//                    //字段聚合
//                    weakness.setVisitDomains(Stream.concat(visitDomains.stream(), dbWeakness.getVisitDomains().stream()).distinct().collect(Collectors.toList()));
//                    weakness.setDeployDomains(Stream.concat(deployDomains.stream(), dbWeakness.getDeployDomains().stream()).distinct().collect(Collectors.toList()));
//                    weakness.setActiveTime(Stream.of(eventTimeStamp, dbWeakness.getActiveTime()).max(Long::compareTo).orElse(eventTimeStamp));
//                }
//            } catch (SQLException e) {
//                log.error("query weakness error:{}", e);
//            }
//        }
//        return weaknessList;
//    }

    public List<WeaknessMetadata> recognitionWeakness(Object sourceEvent) {
        List<WeaknessMetadata> weaknessList = new ArrayList<>();
        EventContext context = new EventContext(sourceEvent);
        for (WeaknessRule weaknessRule : weaknessRules) {
            CompiledRule compiledRule = weaknessRule.getCompiledRule();
            try {
                DecisionResult decisionResult = ruleEngine.eval(compiledRule, context);
                if (decisionResult.isSuccess()) {
                    WeaknessMetadata weaknessMetadata = new WeaknessMetadata();
                    weaknessMetadata.setWeaknessId(weaknessRule.getId());
                    weaknessMetadata.setName(weaknessRule.getName());
                    weaknessMetadata.setType(weaknessRule.getType());
                    weaknessMetadata.setLevel(weaknessRule.getLevel());
                    weaknessMetadata.setState(State.NEW.value());
                    weaknessList.add(weaknessMetadata);
                }
            } catch (Exception e) {
                log.error("recognitionWeakness {} error", weaknessRule.getName(), e);
            }
        }
        return weaknessList;
    }


    private List<WeaknessRule> getWeaknessRules() {
        List<WeaknessRule> weaknessRuleList = new ArrayList<>();
        try {
            List<Map<String, Object>> querys = queryRunner.query("select id,name,type,level,matchRule from " + weaknessConfig.getProperty(WeaknessConstant.WEAKNESS_RULE_TABLE, "weakness_rule"), new MapListHandler());
            for (Map<String, Object> query : querys) {
                WeaknessRule weaknessRule = new WeaknessRule();
                weaknessRule.setId((Integer) query.get("id"));
                weaknessRule.setName((String) query.get("name"));
                weaknessRule.setType((String) query.get("type"));
                weaknessRule.setLevel((Integer) query.get("level"));
                weaknessRule.setMatchRule(JSON.parseObject(String.valueOf(query.get("matchRule")), MatchRule.class));
                weaknessRuleList.add(weaknessRule);
            }
        } catch (SQLException e) {
            log.error("query WeaknessRules error", e);
        }
        return weaknessRuleList;
    }

    private WeaknessRule createTestWeaknessRule() {
        WeaknessRule weaknessRule = new WeaknessRule();
        weaknessRule.setId(1);
        weaknessRule.setType("testType");
        weaknessRule.setName("testWeakness");
        weaknessRule.setLevel(1);

        MatchRule matchRule = new MatchRule();
        matchRule.setId("matchId1");

        RuleCondition ruleCondition = new RuleCondition();
        ruleCondition.setSeq("1");
        ruleCondition.setFeature("dbVersion");
        ruleCondition.setFeatureType(FeatureDefault.FEATURE);
        ruleCondition.setOperator("EQ");
        ruleCondition.setValue("5.7");
        ruleCondition.setValueType(ValueType.PRIMITIVE.name());

        matchRule.setRuleConditions(Collections.singletonList(ruleCondition));
        Decision decision = new Decision();
        decision.setLogic("1");
        matchRule.setDecision(decision);

        weaknessRule.setMatchRule(matchRule);
        return weaknessRule;
    }


}