package com.quanzhi.audit.weakness.entity;

import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import lombok.Data;

/**
 * <AUTHOR>
 * create at 2025/5/8 19:26
 * @description:
 **/
@Data
public class WeaknessRule {
    private Integer id;
    private String name;
    private String type;
    private int level;
    private MatchRule matchRule;
    private CompiledRule compiledRule;
}