package com.quanzhi.omni.audit.parse;

import com.quanzhi.sqlparser.SqlParser;
import com.quanzhi.sqlparser.context.DatabaseType;
import com.quanzhi.sqlparser.view.SqlParserResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;

public class SqlParseProcessor implements Serializable {

    private static final Logger log = LoggerFactory.getLogger(SqlParseProcessor.class);
    private final SqlParser sqlParser = SqlParser.getInstance();

    public SqlParserResult process(String sql) {
        return sqlParser.parsePostgreSQL(sql);
    }

    public SqlParserResult process(String sql, String dbType) {
        try {
            if(dbType == null){
                return null;
            }
            if(dbType.equalsIgnoreCase("mysql")){
                return sqlParser.parseMysql(sql);
            } else if (dbType.equalsIgnoreCase("sqlserver")) {
                return sqlParser.parseSqlServer(sql);
            } else if (dbType.equalsIgnoreCase("mongodb")) {
                return sqlParser.parseMongodb(sql);
            } else if (dbType.equalsIgnoreCase("oracle")){
                return sqlParser.parseOracle(sql);
            } else if (dbType.equalsIgnoreCase("mariadb")){
                return sqlParser.parseMariaDB(sql);
            } else if (dbType.equalsIgnoreCase("dm")){
                return sqlParser.parseDM(sql);
            } else if (dbType.equalsIgnoreCase("gbase")){
                return sqlParser.parseGbase(sql);
            } else if (dbType.equalsIgnoreCase("postgresql") || dbType.equalsIgnoreCase("postgre")) {
                return sqlParser.parsePostgreSQL(sql);
            }
        } catch (Exception e){
            log.error("sql:{} parse error", sql, e);
        }
        return null;
    }

}
