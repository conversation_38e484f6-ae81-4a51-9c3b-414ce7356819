package com.quanzhi.omni.audit.query.jdbc;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import javax.sql.DataSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DataSourceFactory {

    private static final Map<String, DataSource> dataSourceMap = new ConcurrentHashMap<>();

    public static DataSource createDataSource(JdbcConfig config) {
        valid(config);
        DataSource dataSource = dataSourceMap.get(config.getJdbcUrl());
        if (dataSource == null) {
            synchronized (dataSourceMap) {
                dataSource = dataSourceMap.get(config.getJdbcUrl());
                if (dataSource == null) {
                    dataSource = getDataSource(config);
                    dataSourceMap.put(config.getJdbcUrl(), dataSource);
                }
            }
        }
        return dataSource;
    }

    private static void valid(JdbcConfig config) {
        if (config.getJdbcUrl() == null || config.getJdbcUrl().isEmpty()) {
            throw new IllegalArgumentException("jdbc url must not empty");
        }
        if (config.getDriverClassName() == null || config.getDriverClassName().isEmpty()) {
            throw new IllegalArgumentException("driverClassName must not empty");
        }
    }

    private static DataSource getDataSource(JdbcConfig config) {
        HikariConfig hikariConfig;
        if (config.getPoolProperties() == null) {
            hikariConfig = new HikariConfig();
        } else {
            hikariConfig = new HikariConfig(config.getPoolProperties());
        }
        hikariConfig.setDriverClassName(config.getDriverClassName());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        if (config.getConnectionTimeout() > 0) {
            hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
        }
        if (config.getIdleTimeout() > 0) {
            hikariConfig.setIdleTimeout(config.getIdleTimeout());
        }
        if (config.getMaxLifetime() > 0) {
            hikariConfig.setMaxLifetime(config.getMaxLifetime());
        }
        if (config.getMaxPoolSize() > 0) {
            hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        }
        if (config.getMinIdle() > 0) {
            hikariConfig.setMinimumIdle(config.getMinIdle());
        }
        if (config.getValidationTimeout() > 0) {
            hikariConfig.setValidationTimeout(config.getValidationTimeout());
        }
        if (config.getLeakDetectionThreshold() > 0) {
            hikariConfig.setLeakDetectionThreshold(config.getLeakDetectionThreshold());
        }
        if (config.getDataSourceProperties() != null) {
            hikariConfig.setDataSourceProperties(config.getDataSourceProperties());
        }
        return new HikariDataSource(hikariConfig);
    }

    public static void close() {
        synchronized (dataSourceMap) {
            dataSourceMap.forEach((name, dataSource) -> {
                if (dataSource instanceof HikariDataSource) {
                    ((HikariDataSource) dataSource).close();
                }
            });
            dataSourceMap.clear();
        }
    }
}
