package com.quanzhi.omni.audit.query.utils;

/**
 * Utility class for column name transformations
 */
public class ColumnNameUtils {

    /**
     * Converts a snake_case string to camelCase
     * For example: "keep_enable" -> "keepEnable"
     *
     * @param snakeCase the snake_case string to convert
     * @return the camelCase version of the input string
     */
    public static String snakeToCamelCase(String snakeCase) {
        if (snakeCase == null || snakeCase.isEmpty()) {
            return snakeCase;
        }

        // Split the string by underscore
        String[] parts = snakeCase.split("_");
        StringBuilder camelCase = new StringBuilder(parts[0].toLowerCase());

        // Convert each part after the first one to have its first letter capitalized
        for (int i = 1; i < parts.length; i++) {
            if (parts[i].length() > 0) {
                camelCase.append(Character.toUpperCase(parts[i].charAt(0)));
                if (parts[i].length() > 1) {
                    camelCase.append(parts[i].substring(1).toLowerCase());
                }
            }
        }

        return camelCase.toString();
    }
}
