package com.quanzhi.omni.audit.query.processor;

import org.apache.commons.dbutils.BeanProcessor;

import java.sql.Array;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * create at 2025/5/13 18:03
 * @description:
 **/
public class CustomBeanProcessor extends BeanProcessor {

    @Override
    protected Object processColumn(ResultSet rs, int index, Class<?> propType) throws SQLException {
        if (propType.equals(List.class)) {
            Array array = rs.getArray(index);
            if (array == null) {
                return new ArrayList<>(); // 返回空列表，避免 null
            }
            Object[] objects = (Object[]) array.getArray();
            return Arrays.asList(objects)
                    .stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());
        }
        return super.processColumn(rs, index, propType);   }
}