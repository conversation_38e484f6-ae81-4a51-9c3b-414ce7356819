package com.quanzhi.omni.audit.query.handler;

import com.quanzhi.omni.audit.query.processor.CamelCaseBeanProcessor;
import org.apache.commons.dbutils.BasicRowProcessor;
import org.apache.commons.dbutils.RowProcessor;
import org.apache.commons.dbutils.handlers.BeanHandler;

/**
 * A ResultSetHandler implementation that converts a ResultSet row into a JavaBean.
 * This handler converts snake_case column names to camelCase before mapping them to bean properties.
 *
 * @param <T> the target bean type
 */
public class CamelCaseBeanHandler<T> extends BeanHandler<T> {

    /**
     * Creates a new instance of CamelCaseBeanHandler.
     *
     * @param type The Class that objects returned from handle() are created from
     */
    public CamelCaseBeanHandler(Class<T> type) {
        super(type, createCamelCaseRowProcessor());
    }

    /**
     * Creates a new instance of CamelCaseBeanHandler with a custom RowProcessor.
     *
     * @param type The Class that objects returned from handle() are created from
     * @param convert The RowProcessor implementation to use when converting rows
     */
    public CamelCaseBeanHandler(Class<T> type, RowProcessor convert) {
        super(type, convert);
    }

    /**
     * Creates a RowProcessor that uses CamelCaseBeanProcessor.
     *
     * @return A RowProcessor that uses CamelCaseBeanProcessor
     */
    private static RowProcessor createCamelCaseRowProcessor() {
        return new BasicRowProcessor(new CamelCaseBeanProcessor());
    }
}
