package com.quanzhi.omni.audit.query.handler;

import com.quanzhi.omni.audit.query.processor.CamelCaseBeanProcessor;
import org.apache.commons.dbutils.BasicRowProcessor;
import org.apache.commons.dbutils.RowProcessor;
import org.apache.commons.dbutils.handlers.BeanListHandler;

import java.util.List;

/**
 * A ResultSetHandler implementation that converts a ResultSet into a List of JavaBeans.
 * This handler converts snake_case column names to camelCase before mapping them to bean properties.
 *
 * @param <T> the target bean type
 */
public class CamelCaseBeanListHandler<T> extends <PERSON><PERSON>istHandler<T> {

    /**
     * Creates a new instance of CamelCase<PERSON>eanListHandler.
     *
     * @param type The Class that objects returned from handle() are created from
     */
    public CamelCaseBeanListHandler(Class<T> type) {
        super(type, createCamelCaseRowProcessor());
    }

    /**
     * Creates a new instance of CamelCaseBeanListHand<PERSON> with a custom RowProcessor.
     *
     * @param type The Class that objects returned from handle() are created from
     * @param convert The RowProcessor implementation to use when converting rows
     */
    public CamelCaseBeanListHandler(Class<T> type, RowProcessor convert) {
        super(type, convert);
    }

    /**
     * Creates a RowProcessor that uses CamelCaseBeanProcessor.
     *
     * @return A RowProcessor that uses CamelCaseBeanProcessor
     */
    private static RowProcessor createCamelCaseRowProcessor() {
        return new BasicRowProcessor(new CamelCaseBeanProcessor());
    }
}
