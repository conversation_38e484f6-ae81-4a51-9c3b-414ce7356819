package omni.audit.asset.handle.handler.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.domain.facade.RuleFacade;
import com.quanzhi.re.core.initializer.RuleEngineInitializer;
import com.quanzhi.re.core.utils.SpringBeanUtil;
import com.quanzhi.re.infrastructure.config.PostgresConfig;
import com.quanzhi.re.infrastructure.factory.RepositoryFactory;
import lombok.Getter;
import omni.audit.asset.handle.entity.CompiledRuleDetail;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.handler.AbstractHandler;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RelatedVariable;
import com.quanzhi.re.core.variable.facade.VariableFacade;
import lombok.extern.slf4j.Slf4j;
import omni.audit.common.util.HandleUtil;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapListHandler;

import java.sql.SQLException;
import java.util.*;

@Slf4j
public class LevelHandler extends AbstractHandler {

    private List<RelatedVariable> relatedLeftVarOrders = new ArrayList<>();

    private VariableFacade variableFacade;

    private final RuleEngine ruleEngine = new RuleEngine();

    private RuleFacade ruleFacade;


    private Map<String,CompiledRule> compiledRuleMap = new HashMap<>(3);

    @Getter
    public PostgresConfig POSTGRES_CONFIG;

    @Getter
    public RepositoryFactory REPOSITORY_FACTORY;


    public LevelHandler(){
        relatedLeftVarOrders = new ArrayList<>();
        POSTGRES_CONFIG = new PostgresConfig("*********************************************","postgres","password123","public");
        REPOSITORY_FACTORY = new RepositoryFactory(POSTGRES_CONFIG);
        // 初始化决策引擎
        RuleEngineInitializer.initialize(REPOSITORY_FACTORY.getFunctionRepository(),REPOSITORY_FACTORY.getFeatureRepository(),REPOSITORY_FACTORY.getVariableRepository(),REPOSITORY_FACTORY.getRuleRepository());
        ruleFacade = SpringBeanUtil.getBean(RuleFacade.class);
        variableFacade = SpringBeanUtil.getBean(VariableFacade.class);
        init();
    }

    private void init() {
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        List<Map<String, Object>> mapList = null;
        try {
            mapList = queryRunner.query("select * from level_rule", new MapListHandler());
        } catch (SQLException e) {
            log.error("query level rules error:",e);
        }

        if (mapList != null && !mapList.isEmpty()) {
            for (Map<String, Object> stringObjectMap : mapList) {
                Object levelType = stringObjectMap.get("level_type");
                Object matchRuleObj = stringObjectMap.get("match_rule");
                if (levelType != null && matchRuleObj != null) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    try {
                        MatchRule matchRule = objectMapper.readValue((String) matchRuleObj, MatchRule.class);
                        relatedLeftVarOrders.addAll(ruleFacade.queryVarOrder(matchRule).getRelatedLeftVarOrder());
                        compiledRuleMap.put((String) levelType,ruleEngine.compile(matchRule));
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
    }


    @Override
    public String name() {
        return "LevelHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        List<Map<String, String>> assetLevelParam = handleMap.getAssetLevelParam();

        if(!compiledRuleMap.isEmpty() ){
            compiledRuleMap.forEach(
                    (k,v)->{
                        for (Map<String, String> map : assetLevelParam) {
                            try {
                                if (map.get("levelType").equals(k)) {
                                    String level = "l1";

                                    EventContext context = new EventContext(eventMap);
                                    Set<String> vars = new HashSet<>();
                                    if(relatedLeftVarOrders != null && !relatedLeftVarOrders.isEmpty()){
                                        for (RelatedVariable relatedVariable:relatedLeftVarOrders){
                                            if(FeatureDefault.GENERAL_VARIABLE.equals(relatedVariable.getType())){
                                                if(!vars.contains(relatedVariable.getValue())){
                                                    context.assignValue(relatedVariable.getValue(),variableFacade.calculatedValue(relatedVariable.getValue(),context));
                                                    vars.add(relatedVariable.getValue());
                                                }
                                            }
                                        }
                                    }
                                    DecisionResult decisionResult = ruleEngine.eval(v, context);
                                    if(decisionResult.isSuccess()){
                                        level = v.getId();
                                    }
                                    // 判断资产的敏感等级是否低于当前事件的敏感等级，如果是则更新资产的敏感等级
                                    String path = map.get("assetLevelPath");
                                    HandleUtil.updateValueByPath(eventMap,path,level);
                                }
                            }catch (Exception e) {
                                log.error("level handler error:",e);
                            }
                        }
                    }
            );
        }
    }

}
