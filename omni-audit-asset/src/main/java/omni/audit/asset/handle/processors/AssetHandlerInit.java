package omni.audit.asset.handle.processors;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.entity.HandlerEntity;
import omni.audit.asset.handle.handler.AbstractHandler;
import lombok.extern.slf4j.Slf4j;
import org.reflections.Reflections;

import java.util.*;

@Slf4j
public class AssetHandlerInit {
    private List<AbstractHandler> abstractHandlers = new ArrayList<>();

    private Map<String,Map<String,String>> tableKeyMap=new HashMap<>();
    private Map<String,String> eventKeyMap=new HashMap<>();

    private List<String> handlerNames;

    public AssetHandlerInit(List<String> handlerNames){
        this.handlerNames=handlerNames;
        register();

    }

    private void register(){
        String path="omni.audit.asset.handle.handler.impl";
        Reflections reflections = new Reflections(path);
        Set<Class<? extends AbstractHandler>> handlerSet = reflections.getSubTypesOf(AbstractHandler.class);
        for (String handlerName : handlerNames) {
            for (Class<? extends AbstractHandler> handler : handlerSet) {
                if (handler.getName().equals(path+"."+handlerName)) {
                    try {
                        abstractHandlers.add(handler.getDeclaredConstructor().newInstance());
                    } catch (Exception e) {
                        log.error("handler init error",e);
                    }
                }
            }
        }
    }

    public void handle(HandleMap handleMap){
        for (AbstractHandler abstractHandler : abstractHandlers) {
            abstractHandler.handle(handleMap);
        }
    }

}
