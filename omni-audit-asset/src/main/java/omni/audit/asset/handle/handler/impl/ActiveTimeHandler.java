package omni.audit.asset.handle.handler.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.entity.HandlerEntity;
import omni.audit.asset.handle.handler.AbstractHandler;
import omni.audit.common.util.HandleUtil;

import java.util.List;
import java.util.Map;

public class ActiveTimeHandler extends AbstractHandler {
    @Override
    public String name() {
        return "ActiveTimeHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        Map<String, String> assetTimeParam = handleMap.getAssetTimeParam();
        Object timestamp = eventMap.get(assetTimeParam.get("timestamp"));
        for (String key : assetTimeParam.keySet()) {
            if (key.contains("activeTime") || key.contains("ActiveTime")){
                String path = assetTimeParam.get(key);
                Map<String,Object> temp = eventMap;
                HandleUtil.updateMapValue(temp,path.split("\\."),0,timestamp,true);
            }
        }
    }
}
