package omni.audit.asset.handle.handler.impl;

import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.handler.AbstractHandler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.MatchResult;

public class AssignmentHandler extends AbstractHandler {
    @Override
    public String name() {
        return "AssignmentHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        List<Map<String, String>> assetAssignmentParam = handleMap.getAssetAssignmentParam();

        for (Map<String, String> assignment : assetAssignmentParam) {
            String sourceKey = assignment.get("sourceKey");
            String targetKey = assignment.get("targetKey");

            // 解析 sourceKey 中的占位符（支持多层级，如 ${a.b.c}）
            String resolvedValue = resolveNestedPlaceholders(sourceKey, eventMap);

            // 将解析后的值设置到 targetKey（支持多层级，如 x.y.z）
            if (targetKey != null && !targetKey.isEmpty()) {
                setNestedValue(eventMap, targetKey, resolvedValue);
            }
        }
    }

    /**
     * 解析多层级占位符，如 ${a.b.c}
     */
    private String resolveNestedPlaceholders(String template, Map<String, Object> dataMap) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        // 正则匹配 ${xxx.yyy.zzz} 格式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\$\\{(.+?)\\}");
        java.util.regex.Matcher matcher = pattern.matcher(template);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String path = matcher.group(1); // 如 "a.b.c"
            Object value = getNestedValue(dataMap, path);
            String replacement = (value != null) ? value.toString() : "";
            matcher.appendReplacement(result, replacement);
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 从嵌套 Map 中获取值，如路径 "a.b.c" → map.get("a").get("b").get("c")
     */
    private Object getNestedValue(Map<String, Object> dataMap, String path) {
        String[] keys = path.split("\\.");
        Object current = dataMap;

        for (String key : keys) {
            if (current instanceof Map) {
                current = ((Map<String, Object>) current).get(key);
            } else {
                return null; // 路径中断，返回 null
            }
        }
        return current;
    }

    /**
     * 设置嵌套值，如路径 "x.y.z" → 如果 x 或 y 不存在，则动态创建嵌套 Map
     */
    private void setNestedValue(Map<String, Object> dataMap, String path, Object value) {
        String[] keys = path.split("\\.");
        Map<String, Object> currentMap = dataMap;

        // 遍历到倒数第二个 key，确保路径存在
        for (int i = 0; i < keys.length - 1; i++) {
            String key = keys[i];
            Object next = currentMap.get(key);

            if (!(next instanceof Map)) {
                // 如果路径不存在，则创建新的嵌套 Map
                next = new HashMap<>();
                currentMap.put(key, next);
            }
            currentMap = (Map<String, Object>) next;
        }

        // 设置最终的值
        currentMap.put(keys[keys.length - 1], value);
    }
}
