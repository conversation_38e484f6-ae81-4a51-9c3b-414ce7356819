package omni.audit.asset.handle.handler.impl;

import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import lombok.extern.slf4j.Slf4j;
import omni.audit.asset.handle.entity.HandleMap;
import omni.audit.asset.handle.handler.AbstractHandler;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapHandler;
import org.apache.commons.dbutils.handlers.MapListHandler;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class MajorKeyHandler extends AbstractHandler {
    QueryRunner queryRunner;

    public MajorKeyHandler(){
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();
        queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
    }

    @Override
    public String name() {
        return "MajorKeyHandler";
    }

    @Override
    public void handle(HandleMap handleMap) {
        Map<String, Object> eventMap = handleMap.getEventMap();
        Map<String, Object> assetIdParam = handleMap.getAssetIdParam();
        for (String tableName : assetIdParam.keySet()) {
            StringBuilder sb = new StringBuilder();
            Map<String,Object> assetMap = (Map<String,Object> )assetIdParam.get(tableName); // 获取如 "a,b.c.d,e.?[f!g!h]" 的表达式
            String pathExpression = (String) assetMap.get("assetIdPath");
            String assetIdName = (String)assetMap.get("assetIdName");
            String assetIdEvent = (String)assetMap.get("assetIdEvent");
            Map<String,String> sqlKeyAndEventKey = (Map<String,String> )assetMap.get("sqlKeyAndEventKey");
            // 1. 用逗号分割多个独立路径
            String[] paths = pathExpression.split(",");
            for (int i = 0; i < paths.length; i++) {
                String path = paths[i];
                path = path.trim(); // 去除首尾空格

                // 2. 检查是否包含 .?[keys] 语法
                if (path.contains(".?[")) {
                    // 示例：将 "e.?[f!g!h]" 拆分为:
                    // - mapPath = "e"
                    // - keys = ["f", "g", "h"]
                    int bracketPos = path.indexOf(".?[");
                    String mapPath = path.substring(0, bracketPos); // 获取 ? 前的路径
                    String keysStr = path.substring(bracketPos + 3, path.length() - 1); // 取出括号内内容
                    String[] keys = keysStr.split("!");

                    // 获取目标映射（可能是嵌套的多层map）
                    Object target = getNestedValue(eventMap, mapPath);

                    if (target instanceof Map) {
                        Map<?, ?> map = (Map<?, ?>) target;
                        for (Object value : map.values()) {
                            if (value instanceof Map) {
                                Map<String, Object> map1 = (Map<String, Object>) value;
                                String sbString = sb.toString();
                                for (String key : keys) {
                                    Object object = map1.get(key);
                                    String assetId = sbString + object;
                                    Map<String, Object> sqlData = getSqlData(tableName, assetIdName, assetId);
                                    fillData(assetId,assetIdEvent,sqlKeyAndEventKey,sqlData,map1);
                                }
                            }
                        }
                    } else {
                        System.err.println("路径 " + mapPath + " 不是有效的映射结构");
                    }
                }
                else {
                    // 普通路径处理（如 "a" 或 "b.c.d"）
                    Object value = getNestedValue(eventMap, path);
                    sb.append(value);
                    if (i == paths.length - 1) {
                        //最后一位
                        String assetId = sb.toString();
                        Map<String, Object> sqlData = getSqlData(tableName, assetIdName, assetId);
                        fillData(assetId,assetIdEvent,sqlKeyAndEventKey,sqlData,eventMap);
                    }
                }
            }

        }
    }

    private void fillData(String assetId,String assetIdEvent, Map<String,String> sqlKeyAndEventKey, Map<String, Object> sqlMap, Map<String, Object> eventMap){
        if (assetIdEvent != null){
            eventMap.put(assetIdEvent, assetId);
        }
        if (sqlMap == null || sqlMap.isEmpty()) {
            return;
        }
        for (String sqlKey : sqlKeyAndEventKey.keySet()) {
            Object object = sqlMap.get(sqlKey);
            String eventKey = sqlKeyAndEventKey.get(sqlKey);
            eventMap.put(eventKey, object);
        }
    }

    private Map<String, Object> getSqlData(String tableName,String assetIdName,String string){
        Map<String, Object> map = new HashMap<>();
        try {
            map = queryRunner.query("select * from " + tableName + " where " + assetIdName + " = '" + string+ "'", new MapHandler());
        } catch (Exception e) {
            log.error(" sql select error",e);
        }
        return map;
    }

    private Object getNestedValue(Map<String, Object> rootMap, String path) {
        String[] keys = path.split("\\.");
        Object current = rootMap;

        for (String key : keys) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(key);
                if (current == null) return null; // 中途遇到null直接返回
            } else {
                return null; // 路径中断
            }
        }
        return current;
    }

}
