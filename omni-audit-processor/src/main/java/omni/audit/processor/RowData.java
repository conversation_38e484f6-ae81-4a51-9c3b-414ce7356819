package omni.audit.processor;

/**
 * <AUTHOR>
 * @class RowData
 * @created 2025/5/6 10:57
 * @desc
 **/
public class RowData {

    private final Object[] fields;

    private volatile int size;

    public RowData(int arity) {
        this.fields = new Object[arity];
    }

    public RowData(Object[] fields) {
        this.fields = fields;
    }

    public int getArity() {
        return fields.length;
    }

    public Object[] getFields() {
        return fields;
    }

    public Object getField(int pos) {
        return this.fields[pos];
    }

    public RowData copy() {
        Object[] newFields = new Object[this.getArity()];
        System.arraycopy(this.getFields(), 0, newFields, 0, newFields.length);
        RowData newRow = new RowData(newFields);
        return newRow;
    }

    public RowData copy(int[] indexMapping) {
        Object[] newFields = new Object[indexMapping.length];
        for (int i = 0; i < indexMapping.length; i++) {
            newFields[i] = this.fields[indexMapping[i]];
        }
        RowData newRow = new RowData(newFields);
        return newRow;
    }

    public Object get(MetaData metaData, String name) {
        Integer index = metaData.find(name);
        if (index == null || index < 0) {
            throw new RuntimeException("Field not found: " + name);
        }
        return this.fields[index];
    }
}
