package omni.audit.processor;

/**
 * <AUTHOR>
 * @class DataTypeEnum
 * @created 2025/5/6 15:39
 * @desc
 **/
public enum DataTypeEnum {
    STRING("String"),
    INT("Int"),
    LONG("Long"),
    DOUBLE("Double"),
    BOOLEAN("Boolean"),
    DATE("Date"),
    LIST("List"),
    SET("Set"),
    MAP("Map"),
    ;

    private String value;

    DataTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
