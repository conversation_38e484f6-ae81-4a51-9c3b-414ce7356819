package omni.audit.processor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @class MetaData
 * @created 2025/5/6 14:47
 * @desc
 **/
public class MetaData {

    /**
     * identifier
     */
    private String id;

    /**
     * table name
     * db event
     */
    private String name;

    private String desc;

    /**
     * 字段列表
     */
    private List<Field> fields;

    private transient Map<String, Integer> fieldIndexMap;

    /**
     * 根据字段名查找字段索引
     * @param fieldName
     * @return -1 表示未找到
     */
    public Integer find(String fieldName) {
        if (fieldIndexMap == null) {
            return -1;
        }
        if (fieldName == null || fieldName.isEmpty()) {
            return -1;
        }
        return fieldIndexMap.get(fieldName);
    }

}
