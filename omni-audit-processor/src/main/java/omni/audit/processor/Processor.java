package omni.audit.processor;

import java.util.Properties;

/**
 * <AUTHOR>
 * @interface Processor
 * @created 2025/4/27 17:59
 * @desc 通用的处理器接口
 *
 * <p>
 * 接口定义了处理器的基本生命周期方法，包括初始化、打开、关闭、销毁和执行处理逻辑。
 * 这些方法描述了处理器的声明周期: init -> open -> process -> close -> destroy
 * </p>
 **/
public interface Processor {

    void init(Properties properties) throws Exception;

    void open() throws Exception;

    /**
     * @param context 上下文
     * @param rowData 使用类型系统构建的数据模型
     * @throws Exception
     */
    default void process(Context context, RowData rowData) throws Exception{
        Object uri = rowData.get(null, "uri");
    }

    /**
     * @param context 上下文
     * @throws Exception
     */
    void close(Context context) throws Exception;

    void destroy() throws Exception;

}
