package omni.audit.label.handle;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.quanzhi.omni.audit.query.jdbc.DataSourceFactory;
import com.quanzhi.omni.audit.query.jdbc.JdbcConfig;
import com.quanzhi.re.core.RuleEngine;
import com.quanzhi.re.core.defaults.FeatureDefault;
import com.quanzhi.re.core.domain.engine.CompiledRule;
import com.quanzhi.re.core.domain.entity.DecisionResult;
import com.quanzhi.re.core.domain.entity.EventContext;
import com.quanzhi.re.core.domain.entity.po.MatchRule;
import com.quanzhi.re.core.domain.entity.po.RelatedVariable;
import com.quanzhi.re.core.domain.facade.RuleFacade;
import com.quanzhi.re.core.initializer.RuleEngineInitializer;
import com.quanzhi.re.core.utils.SpringBeanUtil;
import com.quanzhi.re.core.variable.facade.VariableFacade;
import com.quanzhi.re.infrastructure.config.PostgresConfig;
import com.quanzhi.re.infrastructure.factory.RepositoryFactory;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import omni.audit.label.entity.CompiledRuleDetail;
import omni.audit.label.entity.LabelResult;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.MapListHandler;

import java.sql.SQLException;
import java.util.*;

@Slf4j
public class LabelHandle {

    private List<RelatedVariable> relatedLeftVarOrders = new ArrayList<>();

    private VariableFacade variableFacade;

    private final RuleEngine ruleEngine = new RuleEngine();

    private RuleFacade ruleFacade;

    private Map<String,List<CompiledRule>> compiledRuleMap = new HashMap<>();

    @Getter
    public PostgresConfig POSTGRES_CONFIG;

    @Getter
    public RepositoryFactory REPOSITORY_FACTORY;

    public  LabelHandle(String jdbcUrl,String username,String password,String schema){
        POSTGRES_CONFIG = new PostgresConfig(jdbcUrl, username, password, schema);
        REPOSITORY_FACTORY = new RepositoryFactory(POSTGRES_CONFIG);
        // 初始化决策引擎
        RuleEngineInitializer.initialize(REPOSITORY_FACTORY.getFunctionRepository(),REPOSITORY_FACTORY.getFeatureRepository(),REPOSITORY_FACTORY.getVariableRepository(),REPOSITORY_FACTORY.getRuleRepository());
        ruleFacade = SpringBeanUtil.getBean(RuleFacade.class);
        variableFacade = SpringBeanUtil.getBean(VariableFacade.class);
        init();
    }

    public void init(){
        JdbcConfig jdbcConfig = JdbcConfig.builder().username("postgres").jdbcUrl("*********************************************")
                .password("password123")
                .driverClassName("org.postgresql.Driver")
                .build();
        QueryRunner queryRunner = new QueryRunner(DataSourceFactory.createDataSource(jdbcConfig));
        List<Map<String, Object>> mapList = null;
        try {
            mapList = queryRunner.query("select * from label_rule", new MapListHandler());
        } catch (SQLException e) {
            log.error("query label rules error:",e);
        }
        if (mapList != null && !mapList.isEmpty()) {
            for (Map<String, Object> stringObjectMap : mapList) {
                Object levelType = stringObjectMap.get("label_type");
                Object matchRulesObj = stringObjectMap.get("match_rules");
                List<MatchRule> matchRules = convert(matchRulesObj);
                List<CompiledRule> compiledRules = new ArrayList<>();
                for (MatchRule matchRule : matchRules) {
                    relatedLeftVarOrders.addAll(ruleFacade.queryVarOrder(matchRule).getRelatedLeftVarOrder());
                    compiledRules.add(ruleEngine.compile(matchRule));
                }
                compiledRuleMap.put(levelType.toString(),compiledRules);
            }
        }

    }

    private List<MatchRule>  convert(Object matchRulesObj ){
        ObjectMapper objectMapper = new ObjectMapper();
        List<MatchRule> matchRules = new ArrayList<>();

        try {
            // 情况1：PostgreSQL 返回的是 java.sql.Array 类型
            if (matchRulesObj instanceof java.sql.Array) {
                // 转换为Java数组
                Object[] rulesArray = (Object[]) ((java.sql.Array) matchRulesObj).getArray();

                for (Object ruleObj : rulesArray) {
                    if (ruleObj != null) {
                        // 每个元素都是text类型，直接解析为CompiledRule
                        MatchRule rule = objectMapper.readValue(ruleObj.toString(), MatchRule.class);
                        matchRules.add(rule);
                    }
                }
            }
            // 情况2：某些驱动可能已经转换为String数组
            else if (matchRulesObj instanceof String[]) {
                String[] rulesArray = (String[]) matchRulesObj;
                for (String ruleJson : rulesArray) {
                    if (ruleJson != null && !ruleJson.isEmpty()) {
                        MatchRule rule = objectMapper.readValue(ruleJson, MatchRule.class);
                        matchRules.add(rule);
                    }
                }
            }
            // 情况3：可能存储为单个JSON数组字符串
            else if (matchRulesObj instanceof String) {
                String jsonStr = (String) matchRulesObj;
                if (!jsonStr.trim().isEmpty()) {
                    matchRules = objectMapper.readValue(
                            jsonStr,
                            new TypeReference<List<MatchRule>>() {}
                    );
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("解析PostgreSQL match_rules字段失败", e);
        }
        return matchRules;
    }

    public List<Integer> handle(Map<String,Object> source,String labelType) {
        List<Integer> list = new ArrayList<>();
        try {
            if(!compiledRuleMap.isEmpty()){
                EventContext context = new EventContext(source);
                Set<String> vars = new HashSet<>();
                if(relatedLeftVarOrders != null && !relatedLeftVarOrders.isEmpty()){
                    for (RelatedVariable relatedVariable:relatedLeftVarOrders){
                        if(FeatureDefault.GENERAL_VARIABLE.equals(relatedVariable.getType())){
                            if(!vars.contains(relatedVariable.getValue())){
                                context.assignValue(relatedVariable.getValue(),variableFacade.calculatedValue(relatedVariable.getValue(),context));
                                vars.add(relatedVariable.getValue());
                            }
                        }
                    }
                }
                List<CompiledRule> compiledRuleList = compiledRuleMap.get(labelType);
                if (compiledRuleList == null){
                    return list;
                }
                for (CompiledRule compiledRule : compiledRuleList) {
                    DecisionResult decisionResult = ruleEngine.eval(compiledRule, context);
                    if(decisionResult.isSuccess()){
                        list.add(Integer.valueOf(compiledRule.getId()));
                    }
                }
            }
        } catch (Exception e){
            log.error("query label rules error:",e);
        }
        return list;
    }
}
