<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.quanzhi.omni.audit</groupId>
    <artifactId>omni-audit-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>Omni Audit: Root</name>
    <modules>
        <module>omni-query-jdbc</module>
        <module>omni-audit-label</module>
        <module>omni-audit-asset</module>
        <module>omni-audit-network-segment</module>
        <module>omni-audit-weakness</module>
        <module>omni-audit-common</module>
        <module>omni-audit-filter</module>
        <module>omni-audit-sampling</module>
        <module>omni-audit-seatunnel</module>
        <module>omni-audit-sqlparse</module>
        <module>omni-audit-apex</module>
        <module>omni-audit-handler</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <jackson.version>2.15.4</jackson.version>
        <checkstyle.version>9.3</checkstyle.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>commons-dbutils</groupId>
                <artifactId>commons-dbutils</artifactId>
                <version>1.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>4.0.3</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.36</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.re</groupId>
                <artifactId>ruleEngine-core</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.quanzhi.re</groupId>
                <artifactId>ruleEngine-repository-pgsql</artifactId>
                <version>3.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-api</artifactId>
                <version>2.3.11</version>
            </dependency>
            <dependency>
                <groupId>org.apache.seatunnel</groupId>
                <artifactId>seatunnel-transforms-v2</artifactId>
                <version>2.3.11</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-expression</artifactId>
                <version>5.3.31</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.13.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.36</version>
            </dependency>

        </dependencies>
    </dependencyManagement>



    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>8</source>
                        <target>8</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>

                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>3.3.1</version>
                    <dependencies>
                        <dependency>
                            <groupId>com.puppycrawl.tools</groupId>
                            <artifactId>checkstyle</artifactId>
                            <version>${checkstyle.version}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <id>validate</id>
                            <phase>validate</phase>
                            <goals>
                                <goal>check</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <configLocation>/tools/maven/quanzhi_checkstyle.xml</configLocation>
                        <includeTestSourceDirectory>false</includeTestSourceDirectory>
                        <logViolationsToConsole>true</logViolationsToConsole>
                        <failOnViolation>true</failOnViolation>
                    </configuration>
                </plugin>

            </plugins>
        </pluginManagement>

    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://192.168.0.92:8081/nexus/content/repositories/releases/</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public</id>
            <url>http://192.168.0.92:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>snapshots</id>
            <url>http://192.168.0.92:8081/nexus/content/repositories/snapshots/</url>
        </repository>

    </repositories>

</project>
